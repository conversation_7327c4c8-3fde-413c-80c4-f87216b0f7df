# Car Loan Application - AutoLoan Pro

A modern, customer-facing web application for car loan applications built with Next.js, TypeScript, Supabase, and deployed on Kubernetes.

## Features

### 🚗 Comprehensive Loan Application
- **Multi-step form** with personal details, financial information, and car details
- **Real-time validation** with Zod schemas
- **Auto-calculation** of remaining price and loan amounts
- **File upload** with drag-and-drop support for documents

### 🔒 Security & Privacy
- **GDPR compliant** data handling
- **Bank-level security** with encrypted file storage
- **Input validation** and sanitization
- **Secure API endpoints** with proper error handling

### 📱 Modern UI/UX
- **Responsive design** for mobile and desktop
- **Accessibility features** with proper ARIA labels
- **Loading states** and error handling
- **Progress indicators** for multi-step forms

### ⚡ Performance & Scalability
- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** with shadcn/ui components
- **Kubernetes deployment** with auto-scaling

## Tech Stack

- **Frontend**: Next.js 15, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API Routes, Supabase PostgreSQL
- **File Storage**: Supabase Storage
- **Validation**: Zod, React Hook Form
- **Deployment**: Docker, Kubernetes
- **CI/CD**: GitHub Actions

## Getting Started

### Prerequisites

- Node.js 18 or later
- npm or yarn
- Supabase account
- Docker (for containerization)
- Kubernetes cluster (for deployment)

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd kyc-customer
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Set up Supabase database**
   - Create a new Supabase project
   - Run the migration script: `supabase/migrations/001_initial_schema.sql`
   - Create the storage bucket for loan documents

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Deployment

### Docker

1. **Build the image**
   ```bash
   docker build -t car-loan-app .
   ```

2. **Run the container**
   ```bash
   docker run -p 3000:3000 --env-file .env.local car-loan-app
   ```

### Kubernetes

1. **Update secrets in `k8s/secret.yaml`** with your Supabase credentials
2. **Deploy to Kubernetes**
   ```bash
   kubectl apply -f k8s/
   ```

### CI/CD Pipeline

The GitHub Actions workflow automatically builds, tests, and deploys the application.

## File Structure

```
├── src/
│   ├── app/                 # Next.js app router
│   ├── components/          # React components
│   ├── lib/                # Utility functions
│   └── types/              # TypeScript types
├── supabase/               # Database migrations
├── k8s/                    # Kubernetes manifests
└── .github/workflows/      # CI/CD pipeline
```

## License

This project is licensed under the MIT License.
