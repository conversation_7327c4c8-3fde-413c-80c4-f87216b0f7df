#!/bin/bash

# Development Setup Script for Car Loan Application
# This script helps set up the development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Car Loan Application - Development Setup${NC}"
echo "=============================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Node.js version
echo -e "${YELLOW}📋 Checking Node.js version...${NC}"
if command_exists node; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js version: $NODE_VERSION${NC}"
    
    # Check if version is 18 or higher
    NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    if [ "$NODE_MAJOR_VERSION" -lt 18 ]; then
        echo -e "${RED}❌ Node.js version 18 or higher is required${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ Node.js is not installed${NC}"
    echo -e "${YELLOW}💡 Please install Node.js 18 or higher from https://nodejs.org${NC}"
    exit 1
fi

# Check npm
echo -e "${YELLOW}📦 Checking npm...${NC}"
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✅ npm version: $NPM_VERSION${NC}"
else
    echo -e "${RED}❌ npm is not installed${NC}"
    exit 1
fi

# Install dependencies
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
npm install

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Dependencies installed successfully${NC}"
else
    echo -e "${RED}❌ Failed to install dependencies${NC}"
    exit 1
fi

# Check if .env.local exists
echo -e "${YELLOW}🔧 Checking environment configuration...${NC}"
if [ ! -f ".env.local" ]; then
    echo -e "${YELLOW}📝 Creating .env.local from template...${NC}"
    cp .env.local.example .env.local
    echo -e "${YELLOW}⚠️  Please update .env.local with your Supabase credentials${NC}"
    echo -e "${BLUE}   1. Go to https://supabase.com and create a new project${NC}"
    echo -e "${BLUE}   2. Get your project URL and anon key from Settings > API${NC}"
    echo -e "${BLUE}   3. Update the values in .env.local${NC}"
else
    echo -e "${GREEN}✅ .env.local already exists${NC}"
fi

# Check TypeScript
echo -e "${YELLOW}🔍 Running TypeScript check...${NC}"
npx tsc --noEmit

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ TypeScript check passed${NC}"
else
    echo -e "${RED}❌ TypeScript errors found${NC}"
    echo -e "${YELLOW}💡 Please fix the TypeScript errors before continuing${NC}"
fi

# Check linting
echo -e "${YELLOW}🔍 Running ESLint check...${NC}"
npm run lint

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Linting check passed${NC}"
else
    echo -e "${YELLOW}⚠️  Linting issues found. Run 'npm run lint' to see details${NC}"
fi

# Try to build the application
echo -e "${YELLOW}🔨 Testing build process...${NC}"
npm run build

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Build completed successfully${NC}"
else
    echo -e "${RED}❌ Build failed${NC}"
    echo -e "${YELLOW}💡 Please check the build errors and fix them${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Development setup completed!${NC}"
echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo -e "${BLUE}   1. Update .env.local with your Supabase credentials${NC}"
echo -e "${BLUE}   2. Set up your Supabase database using the migration script${NC}"
echo -e "${BLUE}   3. Run 'npm run dev' to start the development server${NC}"
echo -e "${BLUE}   4. Open http://localhost:3000 in your browser${NC}"
echo ""
echo -e "${YELLOW}🔗 Useful commands:${NC}"
echo "   npm run dev          - Start development server"
echo "   npm run build        - Build for production"
echo "   npm run lint         - Run ESLint"
echo "   npm run type-check   - Run TypeScript check"
echo ""
echo -e "${YELLOW}📚 Documentation:${NC}"
echo "   README.md            - Project documentation"
echo "   supabase/migrations/ - Database schema"
echo "   k8s/                 - Kubernetes deployment files"
