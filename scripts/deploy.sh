#!/bin/bash

# Car Loan Application Deployment Script
# This script helps deploy the application to Kubernetes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="kyc-customer"
APP_NAME="car-loan-app"
IMAGE_NAME="car-loan-app"
IMAGE_TAG="latest"

echo -e "${GREEN}🚀 Car Loan Application Deployment Script${NC}"
echo "=========================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

if ! command_exists kubectl; then
    echo -e "${RED}❌ kubectl is not installed${NC}"
    exit 1
fi

if ! command_exists docker; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Build Docker image
echo -e "${YELLOW}🔨 Building Docker image...${NC}"
docker build -t $IMAGE_NAME:$IMAGE_TAG .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build Docker image${NC}"
    exit 1
fi

# Check if namespace exists
echo -e "${YELLOW}🔍 Checking Kubernetes namespace...${NC}"
if ! kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
    echo -e "${YELLOW}📦 Creating namespace $NAMESPACE...${NC}"
    kubectl apply -f k8s/namespace.yaml
else
    echo -e "${GREEN}✅ Namespace $NAMESPACE already exists${NC}"
fi

# Apply Kubernetes manifests
echo -e "${YELLOW}🚀 Deploying to Kubernetes...${NC}"

# Apply in order
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
kubectl apply -f k8s/hpa.yaml

# Optional: Apply ingress if available
if [ -f "k8s/ingress.yaml" ]; then
    echo -e "${YELLOW}🌐 Applying ingress configuration...${NC}"
    kubectl apply -f k8s/ingress.yaml
fi

# Wait for deployment to be ready
echo -e "${YELLOW}⏳ Waiting for deployment to be ready...${NC}"
kubectl rollout status deployment/$APP_NAME -n $NAMESPACE --timeout=300s

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Deployment completed successfully${NC}"
else
    echo -e "${RED}❌ Deployment failed or timed out${NC}"
    exit 1
fi

# Show deployment status
echo -e "${YELLOW}📊 Deployment Status:${NC}"
kubectl get pods -l app=$APP_NAME -n $NAMESPACE
kubectl get services -n $NAMESPACE

# Get service URL
SERVICE_TYPE=$(kubectl get service $APP_NAME-service -n $NAMESPACE -o jsonpath='{.spec.type}')
if [ "$SERVICE_TYPE" = "LoadBalancer" ]; then
    EXTERNAL_IP=$(kubectl get service $APP_NAME-service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [ -n "$EXTERNAL_IP" ]; then
        echo -e "${GREEN}🌐 Application is available at: http://$EXTERNAL_IP${NC}"
    else
        echo -e "${YELLOW}⏳ Waiting for external IP to be assigned...${NC}"
    fi
elif [ "$SERVICE_TYPE" = "NodePort" ]; then
    NODE_PORT=$(kubectl get service $APP_NAME-service -n $NAMESPACE -o jsonpath='{.spec.ports[0].nodePort}')
    echo -e "${GREEN}🌐 Application is available at: http://<node-ip>:$NODE_PORT${NC}"
else
    echo -e "${YELLOW}💡 Service is ClusterIP. Use port-forward to access:${NC}"
    echo "kubectl port-forward service/$APP_NAME-service 3000:80 -n $NAMESPACE"
fi

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📝 Useful commands:${NC}"
echo "  View logs: kubectl logs -l app=$APP_NAME -n $NAMESPACE"
echo "  Scale app: kubectl scale deployment $APP_NAME --replicas=5 -n $NAMESPACE"
echo "  Port forward: kubectl port-forward service/$APP_NAME-service 3000:80 -n $NAMESPACE"
echo "  Delete app: kubectl delete -f k8s/"
