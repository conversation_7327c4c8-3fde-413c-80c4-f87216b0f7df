-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create loan_applications table
CREATE TABLE loan_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    personal_details JSONB NOT NULL,
    financial_details JSONB NOT NULL,
    car_details JSONB NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'under_review', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create loan_documents table
CREATE TABLE loan_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    loan_application_id UUID NOT NULL REFERENCES loan_applications(id) ON DELETE CASCADE,
    file_name VA<PERSON>HAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    storage_path VARCHAR(500) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_loan_applications_status ON loan_applications(status);
CREATE INDEX idx_loan_applications_created_at ON loan_applications(created_at);
CREATE INDEX idx_loan_documents_loan_application_id ON loan_documents(loan_application_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for loan_applications
CREATE TRIGGER update_loan_applications_updated_at
    BEFORE UPDATE ON loan_applications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE loan_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE loan_documents ENABLE ROW LEVEL SECURITY;

-- Create policies for loan_applications
-- Allow anyone to insert (for new applications)
CREATE POLICY "Allow insert for loan applications" ON loan_applications
    FOR INSERT WITH CHECK (true);

-- Allow users to view their own applications (you might want to add user authentication later)
CREATE POLICY "Allow select for loan applications" ON loan_applications
    FOR SELECT USING (true);

-- Allow updates to applications (for status changes by admins)
CREATE POLICY "Allow update for loan applications" ON loan_applications
    FOR UPDATE USING (true);

-- Create policies for loan_documents
-- Allow anyone to insert documents
CREATE POLICY "Allow insert for loan documents" ON loan_documents
    FOR INSERT WITH CHECK (true);

-- Allow users to view documents
CREATE POLICY "Allow select for loan documents" ON loan_documents
    FOR SELECT USING (true);

-- Create storage bucket for loan documents
INSERT INTO storage.buckets (id, name, public) VALUES ('loan-documents', 'loan-documents', false);

-- Create storage policies
CREATE POLICY "Allow upload of loan documents" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'loan-documents');

CREATE POLICY "Allow viewing of loan documents" ON storage.objects
    FOR SELECT USING (bucket_id = 'loan-documents');

CREATE POLICY "Allow deletion of loan documents" ON storage.objects
    FOR DELETE USING (bucket_id = 'loan-documents');
