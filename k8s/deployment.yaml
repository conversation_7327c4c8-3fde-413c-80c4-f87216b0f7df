apiVersion: apps/v1
kind: Deployment
metadata:
  name: car-loan-app
  namespace: kyc-customer
  labels:
    app: car-loan-app
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: car-loan-app
  template:
    metadata:
      labels:
        app: car-loan-app
        version: v1
    spec:
      containers:
      - name: car-loan-app
        image: car-loan-app:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: car-loan-app-config
              key: NODE_ENV
        - name: NEXT_TELEMETRY_DISABLED
          valueFrom:
            configMapKeyRef:
              name: car-loan-app-config
              key: NEXT_TELEMETRY_DISABLED
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: car-loan-app-config
              key: PORT
        - name: HOSTNAME
          valueFrom:
            configMapKeyRef:
              name: car-loan-app-config
              key: HOSTNAME
        - name: NEXT_PUBLIC_SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: car-loan-app-secrets
              key: NEXT_PUBLIC_SUPABASE_URL
        - name: NEXT_PUBLIC_SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: car-loan-app-secrets
              key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        - name: SUPABASE_SERVICE_ROLE_KEY
          valueFrom:
            secretKeyRef:
              name: car-loan-app-secrets
              key: SUPABASE_SERVICE_ROLE_KEY
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1001
          capabilities:
            drop:
            - ALL
      securityContext:
        fsGroup: 1001
