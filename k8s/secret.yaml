apiVersion: v1
kind: Secret
metadata:
  name: car-loan-app-secrets
  namespace: kyc-customer
type: Opaque
data:
  # Base64 encoded values - replace with your actual Supabase credentials
  # To encode: echo -n "your-value" | base64
  NEXT_PUBLIC_SUPABASE_URL: aHR0cHM6Ly95b3VyLXByb2plY3Quc3VwYWJhc2UuY28=  # https://your-project.supabase.co
  NEXT_PUBLIC_SUPABASE_ANON_KEY: eW91ci1hbm9uLWtleS1oZXJl  # your-anon-key-here
  SUPABASE_SERVICE_ROLE_KEY: eW91ci1zZXJ2aWNlLXJvbGUta2V5LWhlcmU=  # your-service-role-key-here
