apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: car-loan-app-ingress
  namespace: kyc-customer
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - car-loan.yourdomain.com
    secretName: car-loan-app-tls
  rules:
  - host: car-loan.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: car-loan-app-service
            port:
              number: 80
