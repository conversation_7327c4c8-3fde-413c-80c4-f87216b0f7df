{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('es-ES', {\n    style: 'currency',\n    currency: 'EUR',\n  }).format(amount);\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  // Remove all non-digits\n  const cleaned = phone.replace(/\\D/g, '');\n  \n  // Format as XXX XXX XXX\n  if (cleaned.length === 9) {\n    return cleaned.replace(/(\\d{3})(\\d{3})(\\d{3})/, '$1 $2 $3');\n  }\n  \n  return phone;\n}\n\nexport function validateDNI(dni: string): boolean {\n  const dniRegex = /^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/i;\n  \n  if (!dniRegex.test(dni)) {\n    return false;\n  }\n  \n  const letters = 'TRWAGMYFPDXBNJZSQVHLCKE';\n  const number = parseInt(dni.substring(0, 8), 10);\n  const letter = dni.charAt(8).toUpperCase();\n  \n  return letters.charAt(number % 23) === letter;\n}\n\nexport function validateVIN(vin: string): boolean {\n  // Basic VIN validation - 17 characters, alphanumeric except I, O, Q\n  const vinRegex = /^[A-HJ-NPR-Z0-9]{17}$/i;\n  return vinRegex.test(vin);\n}\n\nexport function calculateRemainingPrice(\n  totalPrice: number,\n  tradeInValue: number,\n  downPayment: number\n): number {\n  return Math.max(0, totalPrice - tradeInValue - downPayment);\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\nexport function isValidFileType(file: File): boolean {\n  const allowedTypes = [\n    'application/pdf',\n    'image/jpeg',\n    'image/jpg',\n    'image/png',\n    'image/webp',\n    'application/msword',\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n  ];\n  \n  return allowedTypes.includes(file.type);\n}\n\nexport function generateUniqueId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,kBAAkB,KAAa;IAC7C,wBAAwB;IACxB,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,wBAAwB;IACxB,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,OAAO,QAAQ,OAAO,CAAC,yBAAyB;IAClD;IAEA,OAAO;AACT;AAEO,SAAS,YAAY,GAAW;IACrC,MAAM,WAAW;IAEjB,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM;QACvB,OAAO;IACT;IAEA,MAAM,UAAU;IAChB,MAAM,SAAS,SAAS,IAAI,SAAS,CAAC,GAAG,IAAI;IAC7C,MAAM,SAAS,IAAI,MAAM,CAAC,GAAG,WAAW;IAExC,OAAO,QAAQ,MAAM,CAAC,SAAS,QAAQ;AACzC;AAEO,SAAS,YAAY,GAAW;IACrC,oEAAoE;IACpE,MAAM,WAAW;IACjB,OAAO,SAAS,IAAI,CAAC;AACvB;AAEO,SAAS,wBACd,UAAkB,EAClB,YAAoB,EACpB,WAAmB;IAEnB,OAAO,KAAK,GAAG,CAAC,GAAG,aAAa,eAAe;AACjD;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS,gBAAgB,IAAU;IACxC,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,aAAa,QAAQ,CAAC,KAAK,IAAI;AACxC;AAEO,SAAS;IACd,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod';\nimport { validateDNI, validateVIN } from './utils';\n\nexport const personalDetailsSchema = z.object({\n  fullName: z\n    .string()\n    .min(2, 'Full name must be at least 2 characters')\n    .max(100, 'Full name must be less than 100 characters')\n    .regex(/^[a-zA-ZÀ-ÿ\\u00f1\\u00d1\\s]+$/, 'Full name can only contain letters and spaces'),\n  \n  dniNumber: z\n    .string()\n    .min(9, 'DNI must be 9 characters')\n    .max(9, 'DNI must be 9 characters')\n    .refine(validateDNI, 'Invalid DNI format'),\n  \n  dateOfBirth: z\n    .string()\n    .min(1, 'Date of birth is required')\n    .refine((date) => {\n      const birthDate = new Date(date);\n      const today = new Date();\n      const age = today.getFullYear() - birthDate.getFullYear();\n      return age >= 18 && age <= 100;\n    }, 'You must be between 18 and 100 years old'),\n  \n  phoneNumber: z\n    .string()\n    .min(9, 'Phone number must be at least 9 digits')\n    .regex(/^[0-9\\s]+$/, 'Phone number can only contain digits and spaces'),\n  \n  address: z\n    .string()\n    .min(10, 'Address must be at least 10 characters')\n    .max(200, 'Address must be less than 200 characters'),\n  \n  incomeSource: z.enum(['Salaried', 'Self-Employed', 'Pension'], {\n    required_error: 'Please select an income source',\n  }),\n});\n\nexport const financialDetailsSchema = z.object({\n  loanAmount: z\n    .number()\n    .min(1000, 'Loan amount must be at least €1,000')\n    .max(100000, 'Loan amount cannot exceed €100,000'),\n  \n  totalCarPrice: z\n    .number()\n    .min(1000, 'Car price must be at least €1,000')\n    .max(200000, 'Car price cannot exceed €200,000'),\n  \n  tradeInValue: z\n    .number()\n    .min(0, 'Trade-in value cannot be negative')\n    .max(150000, 'Trade-in value cannot exceed €150,000'),\n  \n  downPayment: z\n    .number()\n    .min(0, 'Down payment cannot be negative')\n    .max(100000, 'Down payment cannot exceed €100,000'),\n}).refine((data) => {\n  const remainingPrice = data.totalCarPrice - data.tradeInValue - data.downPayment;\n  return data.loanAmount <= remainingPrice;\n}, {\n  message: 'Loan amount cannot exceed the remaining price after trade-in and down payment',\n  path: ['loanAmount'],\n});\n\nexport const carDetailsSchema = z.object({\n  vin: z\n    .string()\n    .min(17, 'VIN must be 17 characters')\n    .max(17, 'VIN must be 17 characters')\n    .refine(validateVIN, 'Invalid VIN format'),\n  \n  condition: z.enum(['New', 'Preowned'], {\n    required_error: 'Please select car condition',\n  }),\n  \n  year: z\n    .number()\n    .min(1990, 'Car year must be 1990 or later')\n    .max(new Date().getFullYear() + 1, 'Car year cannot be in the future'),\n  \n  make: z\n    .string()\n    .min(1, 'Car make is required')\n    .max(50, 'Car make must be less than 50 characters'),\n  \n  model: z\n    .string()\n    .min(1, 'Car model is required')\n    .max(50, 'Car model must be less than 50 characters'),\n  \n  trim: z\n    .string()\n    .min(1, 'Car trim is required')\n    .max(50, 'Car trim must be less than 50 characters'),\n});\n\nexport const loanApplicationSchema = z.object({\n  personalDetails: personalDetailsSchema,\n  financialDetails: financialDetailsSchema,\n  carDetails: carDetailsSchema,\n});\n\nexport type PersonalDetailsFormData = z.infer<typeof personalDetailsSchema>;\nexport type FinancialDetailsFormData = z.infer<typeof financialDetailsSchema>;\nexport type CarDetailsFormData = z.infer<typeof carDetailsSchema>;\nexport type LoanApplicationFormData = z.infer<typeof loanApplicationSchema>;\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;;;AAEO,MAAM,wBAAwB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,UAAU,iLAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,KAAK,8CACT,KAAK,CAAC,gCAAgC;IAEzC,WAAW,iLAAA,CAAA,IAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG,4BACP,GAAG,CAAC,GAAG,4BACP,MAAM,CAAC,mHAAA,CAAA,cAAW,EAAE;IAEvB,aAAa,iLAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,GAAG,6BACP,MAAM,CAAC,CAAC;QACP,MAAM,YAAY,IAAI,KAAK;QAC3B,MAAM,QAAQ,IAAI;QAClB,MAAM,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;QACvD,OAAO,OAAO,MAAM,OAAO;IAC7B,GAAG;IAEL,aAAa,iLAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,GAAG,0CACP,KAAK,CAAC,cAAc;IAEvB,SAAS,iLAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI,0CACR,GAAG,CAAC,KAAK;IAEZ,cAAc,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAiB;KAAU,EAAE;QAC7D,gBAAgB;IAClB;AACF;AAEO,MAAM,yBAAyB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,YAAY,iLAAA,CAAA,IAAC,CACV,MAAM,GACN,GAAG,CAAC,MAAM,uCACV,GAAG,CAAC,QAAQ;IAEf,eAAe,iLAAA,CAAA,IAAC,CACb,MAAM,GACN,GAAG,CAAC,MAAM,qCACV,GAAG,CAAC,QAAQ;IAEf,cAAc,iLAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,qCACP,GAAG,CAAC,QAAQ;IAEf,aAAa,iLAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,GAAG,mCACP,GAAG,CAAC,QAAQ;AACjB,GAAG,MAAM,CAAC,CAAC;IACT,MAAM,iBAAiB,KAAK,aAAa,GAAG,KAAK,YAAY,GAAG,KAAK,WAAW;IAChF,OAAO,KAAK,UAAU,IAAI;AAC5B,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAa;AACtB;AAEO,MAAM,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,KAAK,iLAAA,CAAA,IAAC,CACH,MAAM,GACN,GAAG,CAAC,IAAI,6BACR,GAAG,CAAC,IAAI,6BACR,MAAM,CAAC,mHAAA,CAAA,cAAW,EAAE;IAEvB,WAAW,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAW,EAAE;QACrC,gBAAgB;IAClB;IAEA,MAAM,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,MAAM,kCACV,GAAG,CAAC,IAAI,OAAO,WAAW,KAAK,GAAG;IAErC,MAAM,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG,wBACP,GAAG,CAAC,IAAI;IAEX,OAAO,iLAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,yBACP,GAAG,CAAC,IAAI;IAEX,MAAM,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG,wBACP,GAAG,CAAC,IAAI;AACb;AAEO,MAAM,wBAAwB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,iBAAiB;IACjB,kBAAkB;IAClB,YAAY;AACd", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { cn } from \"@/lib/utils\";\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n);\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n));\nLabel.displayName = LabelPrimitive.Root.displayName;\n\nexport { Label };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = \"Textarea\";\n\nexport { Textarea };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Select = SelectPrimitive.Root;\n\nconst SelectGroup = SelectPrimitive.Group;\n\nconst SelectValue = SelectPrimitive.Value;\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n));\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n));\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n));\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName;\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n));\nSelectContent.displayName = SelectPrimitive.Content.displayName;\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n));\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n));\nSelectItem.displayName = SelectPrimitive.Item.displayName;\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n));\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/forms/personal-details-form.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { personalDetailsSchema, PersonalDetailsFormData } from '@/lib/validations';\nimport { formatPhoneNumber } from '@/lib/utils';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Button } from '@/components/ui/button';\n\ninterface PersonalDetailsFormProps {\n  initialData?: Partial<PersonalDetailsFormData>;\n  onSubmit: (data: PersonalDetailsFormData) => void;\n  onNext?: () => void;\n  isLoading?: boolean;\n}\n\nexport function PersonalDetailsForm({\n  initialData,\n  onSubmit,\n  onNext,\n  isLoading = false,\n}: PersonalDetailsFormProps) {\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    watch,\n    formState: { errors, isValid },\n  } = useForm<PersonalDetailsFormData>({\n    resolver: zodResolver(personalDetailsSchema),\n    defaultValues: initialData || {\n      fullName: '',\n      dniNumber: '',\n      dateOfBirth: '',\n      phoneNumber: '',\n      address: '',\n      incomeSource: undefined,\n    },\n    mode: 'onChange',\n  });\n\n  const phoneNumber = watch('phoneNumber');\n  const incomeSource = watch('incomeSource');\n\n  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const formatted = formatPhoneNumber(e.target.value);\n    setValue('phoneNumber', formatted, { shouldValidate: true });\n  };\n\n  const handleFormSubmit = (data: PersonalDetailsFormData) => {\n    onSubmit(data);\n    if (onNext) {\n      onNext();\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900\">Personal Details</h2>\n        <p className=\"text-gray-600 mt-1\">Please provide your personal information</p>\n      </div>\n\n      <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-4\">\n        {/* Full Name */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"fullName\">Full Name *</Label>\n          <Input\n            id=\"fullName\"\n            placeholder=\"e.g., RAMÓN GOMEZ DE LA CRUZ\"\n            {...register('fullName')}\n            className={errors.fullName ? 'border-red-500' : ''}\n          />\n          {errors.fullName && (\n            <p className=\"text-sm text-red-600\">{errors.fullName.message}</p>\n          )}\n        </div>\n\n        {/* DNI Number */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"dniNumber\">DNI Number *</Label>\n          <Input\n            id=\"dniNumber\"\n            placeholder=\"e.g., 01234567A\"\n            maxLength={9}\n            {...register('dniNumber')}\n            className={errors.dniNumber ? 'border-red-500' : ''}\n          />\n          {errors.dniNumber && (\n            <p className=\"text-sm text-red-600\">{errors.dniNumber.message}</p>\n          )}\n        </div>\n\n        {/* Date of Birth */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"dateOfBirth\">Date of Birth *</Label>\n          <Input\n            id=\"dateOfBirth\"\n            type=\"date\"\n            {...register('dateOfBirth')}\n            className={errors.dateOfBirth ? 'border-red-500' : ''}\n          />\n          {errors.dateOfBirth && (\n            <p className=\"text-sm text-red-600\">{errors.dateOfBirth.message}</p>\n          )}\n        </div>\n\n        {/* Phone Number */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"phoneNumber\">Phone Number *</Label>\n          <Input\n            id=\"phoneNumber\"\n            placeholder=\"e.g., 678 159 753\"\n            value={phoneNumber}\n            onChange={handlePhoneChange}\n            className={errors.phoneNumber ? 'border-red-500' : ''}\n          />\n          {errors.phoneNumber && (\n            <p className=\"text-sm text-red-600\">{errors.phoneNumber.message}</p>\n          )}\n        </div>\n\n        {/* Address */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"address\">Address *</Label>\n          <Textarea\n            id=\"address\"\n            placeholder=\"e.g., Avenida de Concha Espina, 1, 28036 Madrid, Spain\"\n            rows={3}\n            {...register('address')}\n            className={errors.address ? 'border-red-500' : ''}\n          />\n          {errors.address && (\n            <p className=\"text-sm text-red-600\">{errors.address.message}</p>\n          )}\n        </div>\n\n        {/* Income Source */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"incomeSource\">Income Source *</Label>\n          <Select\n            value={incomeSource}\n            onValueChange={(value) => setValue('incomeSource', value as any, { shouldValidate: true })}\n          >\n            <SelectTrigger className={errors.incomeSource ? 'border-red-500' : ''}>\n              <SelectValue placeholder=\"Select your income source\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"Salaried\">Salaried</SelectItem>\n              <SelectItem value=\"Self-Employed\">Self-Employed</SelectItem>\n              <SelectItem value=\"Pension\">Pension</SelectItem>\n            </SelectContent>\n          </Select>\n          {errors.incomeSource && (\n            <p className=\"text-sm text-red-600\">{errors.incomeSource.message}</p>\n          )}\n        </div>\n\n        {/* Submit Button */}\n        <div className=\"pt-4\">\n          <Button\n            type=\"submit\"\n            disabled={!isValid || isLoading}\n            className=\"w-full\"\n          >\n            {isLoading ? 'Saving...' : 'Continue to Financial Details'}\n          </Button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAoBO,SAAS,oBAAoB,EAClC,WAAW,EACX,QAAQ,EACR,MAAM,EACN,YAAY,KAAK,EACQ;IACzB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC/B,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA2B;QACnC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,yHAAA,CAAA,wBAAqB;QAC3C,eAAe,eAAe;YAC5B,UAAU;YACV,WAAW;YACX,aAAa;YACb,aAAa;YACb,SAAS;YACT,cAAc;QAChB;QACA,MAAM;IACR;IAEA,MAAM,cAAc,MAAM;IAC1B,MAAM,eAAe,MAAM;IAE3B,MAAM,oBAAoB,CAAC;QACzB,MAAM,YAAY,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,MAAM,CAAC,KAAK;QAClD,SAAS,eAAe,WAAW;YAAE,gBAAgB;QAAK;IAC5D;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,IAAI,QAAQ;YACV;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAGpC,8OAAC;gBAAK,UAAU,aAAa;gBAAmB,WAAU;;kCAExD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAW;;;;;;0CAC1B,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACX,GAAG,SAAS,WAAW;gCACxB,WAAW,OAAO,QAAQ,GAAG,mBAAmB;;;;;;4BAEjD,OAAO,QAAQ,kBACd,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kCAKhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,WAAW;gCACV,GAAG,SAAS,YAAY;gCACzB,WAAW,OAAO,SAAS,GAAG,mBAAmB;;;;;;4BAElD,OAAO,SAAS,kBACf,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;kCAKjE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAc;;;;;;0CAC7B,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACJ,GAAG,SAAS,cAAc;gCAC3B,WAAW,OAAO,WAAW,GAAG,mBAAmB;;;;;;4BAEpD,OAAO,WAAW,kBACjB,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;kCAKnE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAc;;;;;;0CAC7B,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO;gCACP,UAAU;gCACV,WAAW,OAAO,WAAW,GAAG,mBAAmB;;;;;;4BAEpD,OAAO,WAAW,kBACjB,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;kCAKnE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;0CACzB,8OAAC,oIAAA,CAAA,WAAQ;gCACP,IAAG;gCACH,aAAY;gCACZ,MAAM;gCACL,GAAG,SAAS,UAAU;gCACvB,WAAW,OAAO,OAAO,GAAG,mBAAmB;;;;;;4BAEhD,OAAO,OAAO,kBACb,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAe;;;;;;0CAC9B,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,eAAe,CAAC,QAAU,SAAS,gBAAgB,OAAc;wCAAE,gBAAgB;oCAAK;;kDAExF,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAW,OAAO,YAAY,GAAG,mBAAmB;kDACjE,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAgB;;;;;;0DAClC,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;;;;;;;;;;;;;4BAG/B,OAAO,YAAY,kBAClB,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;kCAKpE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,UAAU,CAAC,WAAW;4BACtB,WAAU;sCAET,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/forms/financial-details-form.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { financialDetailsSchema, FinancialDetailsFormData } from '@/lib/validations';\nimport { formatCurrency, calculateRemainingPrice } from '@/lib/utils';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Button } from '@/components/ui/button';\n\ninterface FinancialDetailsFormProps {\n  initialData?: Partial<FinancialDetailsFormData>;\n  onSubmit: (data: FinancialDetailsFormData) => void;\n  onNext?: () => void;\n  onBack?: () => void;\n  isLoading?: boolean;\n}\n\nexport function FinancialDetailsForm({\n  initialData,\n  onSubmit,\n  onNext,\n  onBack,\n  isLoading = false,\n}: FinancialDetailsFormProps) {\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    watch,\n    formState: { errors, isValid },\n  } = useForm<FinancialDetailsFormData>({\n    resolver: zodResolver(financialDetailsSchema),\n    defaultValues: initialData || {\n      loanAmount: 0,\n      totalCarPrice: 0,\n      tradeInValue: 0,\n      downPayment: 0,\n    },\n    mode: 'onChange',\n  });\n\n  const totalCarPrice = watch('totalCarPrice');\n  const tradeInValue = watch('tradeInValue');\n  const downPayment = watch('downPayment');\n  const loanAmount = watch('loanAmount');\n\n  const remainingPrice = calculateRemainingPrice(totalCarPrice, tradeInValue, downPayment);\n\n  // Auto-calculate loan amount when other values change\n  useEffect(() => {\n    if (totalCarPrice > 0) {\n      const maxLoanAmount = Math.max(0, remainingPrice);\n      if (loanAmount === 0 || loanAmount > maxLoanAmount) {\n        setValue('loanAmount', maxLoanAmount, { shouldValidate: true });\n      }\n    }\n  }, [totalCarPrice, tradeInValue, downPayment, remainingPrice, loanAmount, setValue]);\n\n  const handleFormSubmit = (data: FinancialDetailsFormData) => {\n    onSubmit(data);\n    if (onNext) {\n      onNext();\n    }\n  };\n\n  const formatNumberInput = (value: string): number => {\n    const numericValue = value.replace(/[^0-9.]/g, '');\n    return parseFloat(numericValue) || 0;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900\">Financial Details</h2>\n        <p className=\"text-gray-600 mt-1\">Please provide your loan and car pricing information</p>\n      </div>\n\n      <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-4\">\n        {/* Total Car Price */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"totalCarPrice\">Total Car Price *</Label>\n          <div className=\"relative\">\n            <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500\">€</span>\n            <Input\n              id=\"totalCarPrice\"\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              placeholder=\"20,000\"\n              className={`pl-8 ${errors.totalCarPrice ? 'border-red-500' : ''}`}\n              {...register('totalCarPrice', {\n                valueAsNumber: true,\n              })}\n            />\n          </div>\n          {errors.totalCarPrice && (\n            <p className=\"text-sm text-red-600\">{errors.totalCarPrice.message}</p>\n          )}\n        </div>\n\n        {/* Trade-In Value */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"tradeInValue\">Trade-In Value</Label>\n          <div className=\"relative\">\n            <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500\">€</span>\n            <Input\n              id=\"tradeInValue\"\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              placeholder=\"10,000\"\n              className={`pl-8 ${errors.tradeInValue ? 'border-red-500' : ''}`}\n              {...register('tradeInValue', {\n                valueAsNumber: true,\n              })}\n            />\n          </div>\n          {errors.tradeInValue && (\n            <p className=\"text-sm text-red-600\">{errors.tradeInValue.message}</p>\n          )}\n        </div>\n\n        {/* Down Payment */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"downPayment\">Down Payment</Label>\n          <div className=\"relative\">\n            <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500\">€</span>\n            <Input\n              id=\"downPayment\"\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              placeholder=\"0\"\n              className={`pl-8 ${errors.downPayment ? 'border-red-500' : ''}`}\n              {...register('downPayment', {\n                valueAsNumber: true,\n              })}\n            />\n          </div>\n          {errors.downPayment && (\n            <p className=\"text-sm text-red-600\">{errors.downPayment.message}</p>\n          )}\n        </div>\n\n        {/* Remaining Price (Calculated) */}\n        <div className=\"space-y-2\">\n          <Label>Remaining Price (Calculated)</Label>\n          <div className=\"p-3 bg-gray-50 rounded-md border\">\n            <span className=\"text-lg font-semibold text-gray-900\">\n              {formatCurrency(remainingPrice)}\n            </span>\n            <p className=\"text-sm text-gray-600 mt-1\">\n              Total Price - Trade-In Value - Down Payment\n            </p>\n          </div>\n        </div>\n\n        {/* Loan Amount */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"loanAmount\">Loan Amount *</Label>\n          <div className=\"relative\">\n            <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500\">€</span>\n            <Input\n              id=\"loanAmount\"\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              max={remainingPrice}\n              placeholder=\"10,000\"\n              className={`pl-8 ${errors.loanAmount ? 'border-red-500' : ''}`}\n              {...register('loanAmount', {\n                valueAsNumber: true,\n              })}\n            />\n          </div>\n          {errors.loanAmount && (\n            <p className=\"text-sm text-red-600\">{errors.loanAmount.message}</p>\n          )}\n          <p className=\"text-sm text-gray-600\">\n            Maximum loan amount: {formatCurrency(remainingPrice)}\n          </p>\n        </div>\n\n        {/* Summary */}\n        {totalCarPrice > 0 && (\n          <div className=\"p-4 bg-blue-50 rounded-md border border-blue-200\">\n            <h3 className=\"font-semibold text-blue-900 mb-2\">Loan Summary</h3>\n            <div className=\"space-y-1 text-sm\">\n              <div className=\"flex justify-between\">\n                <span>Total Car Price:</span>\n                <span>{formatCurrency(totalCarPrice)}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>Trade-In Value:</span>\n                <span>-{formatCurrency(tradeInValue)}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>Down Payment:</span>\n                <span>-{formatCurrency(downPayment)}</span>\n              </div>\n              <hr className=\"my-2 border-blue-300\" />\n              <div className=\"flex justify-between font-semibold\">\n                <span>Loan Amount:</span>\n                <span>{formatCurrency(loanAmount)}</span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Navigation Buttons */}\n        <div className=\"flex gap-4 pt-4\">\n          {onBack && (\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={onBack}\n              className=\"flex-1\"\n            >\n              Back\n            </Button>\n          )}\n          <Button\n            type=\"submit\"\n            disabled={!isValid || isLoading}\n            className=\"flex-1\"\n          >\n            {isLoading ? 'Saving...' : 'Continue to Car Details'}\n          </Button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAmBO,SAAS,qBAAqB,EACnC,WAAW,EACX,QAAQ,EACR,MAAM,EACN,MAAM,EACN,YAAY,KAAK,EACS;IAC1B,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC/B,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA4B;QACpC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,yHAAA,CAAA,yBAAsB;QAC5C,eAAe,eAAe;YAC5B,YAAY;YACZ,eAAe;YACf,cAAc;YACd,aAAa;QACf;QACA,MAAM;IACR;IAEA,MAAM,gBAAgB,MAAM;IAC5B,MAAM,eAAe,MAAM;IAC3B,MAAM,cAAc,MAAM;IAC1B,MAAM,aAAa,MAAM;IAEzB,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,0BAAuB,AAAD,EAAE,eAAe,cAAc;IAE5E,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,GAAG;YACrB,MAAM,gBAAgB,KAAK,GAAG,CAAC,GAAG;YAClC,IAAI,eAAe,KAAK,aAAa,eAAe;gBAClD,SAAS,cAAc,eAAe;oBAAE,gBAAgB;gBAAK;YAC/D;QACF;IACF,GAAG;QAAC;QAAe;QAAc;QAAa;QAAgB;QAAY;KAAS;IAEnF,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,IAAI,QAAQ;YACV;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,eAAe,MAAM,OAAO,CAAC,YAAY;QAC/C,OAAO,WAAW,iBAAiB;IACrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAGpC,8OAAC;gBAAK,UAAU,aAAa;gBAAmB,WAAU;;kCAExD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAmE;;;;;;kDACnF,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,KAAI;wCACJ,aAAY;wCACZ,WAAW,CAAC,KAAK,EAAE,OAAO,aAAa,GAAG,mBAAmB,IAAI;wCAChE,GAAG,SAAS,iBAAiB;4CAC5B,eAAe;wCACjB,EAAE;;;;;;;;;;;;4BAGL,OAAO,aAAa,kBACnB,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;kCAKrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAe;;;;;;0CAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAmE;;;;;;kDACnF,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,KAAI;wCACJ,aAAY;wCACZ,WAAW,CAAC,KAAK,EAAE,OAAO,YAAY,GAAG,mBAAmB,IAAI;wCAC/D,GAAG,SAAS,gBAAgB;4CAC3B,eAAe;wCACjB,EAAE;;;;;;;;;;;;4BAGL,OAAO,YAAY,kBAClB,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;kCAKpE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAc;;;;;;0CAC7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAmE;;;;;;kDACnF,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,KAAI;wCACJ,aAAY;wCACZ,WAAW,CAAC,KAAK,EAAE,OAAO,WAAW,GAAG,mBAAmB,IAAI;wCAC9D,GAAG,SAAS,eAAe;4CAC1B,eAAe;wCACjB,EAAE;;;;;;;;;;;;4BAGL,OAAO,WAAW,kBACjB,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;kCAKnE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;kDAElB,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAO9C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAa;;;;;;0CAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAmE;;;;;;kDACnF,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,KAAI;wCACJ,KAAK;wCACL,aAAY;wCACZ,WAAW,CAAC,KAAK,EAAE,OAAO,UAAU,GAAG,mBAAmB,IAAI;wCAC7D,GAAG,SAAS,cAAc;4CACzB,eAAe;wCACjB,EAAE;;;;;;;;;;;;4BAGL,OAAO,UAAU,kBAChB,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,UAAU,CAAC,OAAO;;;;;;0CAEhE,8OAAC;gCAAE,WAAU;;oCAAwB;oCACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;oBAKxC,gBAAgB,mBACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;kDAExB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAK;oDAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAK;oDAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;kDAEzB,8OAAC;wCAAG,WAAU;;;;;;kDACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAO9B,8OAAC;wBAAI,WAAU;;4BACZ,wBACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;0CAIH,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU,CAAC,WAAW;gCACtB,WAAU;0CAET,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 1443, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/forms/car-details-form.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { carDetailsSchema, CarDetailsFormData } from '@/lib/validations';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Button } from '@/components/ui/button';\n\ninterface CarDetailsFormProps {\n  initialData?: Partial<CarDetailsFormData>;\n  onSubmit: (data: CarDetailsFormData) => void;\n  onNext?: () => void;\n  onBack?: () => void;\n  isLoading?: boolean;\n}\n\nconst currentYear = new Date().getFullYear();\nconst carMakes = [\n  'Audi', 'BMW', 'Citroën', 'Fiat', 'Ford', 'Honda', 'Hyundai', 'Kia',\n  'Mercedes-Benz', 'Nissan', 'Opel', 'Peugeot', 'Renault', 'SEAT', 'Škoda',\n  'Toyota', 'Volkswagen', 'Volvo', 'Other'\n];\n\nexport function CarDetailsForm({\n  initialData,\n  onSubmit,\n  onNext,\n  onBack,\n  isLoading = false,\n}: CarDetailsFormProps) {\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    watch,\n    formState: { errors, isValid },\n  } = useForm<CarDetailsFormData>({\n    resolver: zodResolver(carDetailsSchema),\n    defaultValues: initialData || {\n      vin: '',\n      condition: undefined,\n      year: currentYear,\n      make: '',\n      model: '',\n      trim: '',\n    },\n    mode: 'onChange',\n  });\n\n  const condition = watch('condition');\n  const make = watch('make');\n\n  const handleFormSubmit = (data: CarDetailsFormData) => {\n    onSubmit(data);\n    if (onNext) {\n      onNext();\n    }\n  };\n\n  const handleVinChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value.toUpperCase().replace(/[^A-HJ-NPR-Z0-9]/g, '');\n    setValue('vin', value, { shouldValidate: true });\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900\">Car Details</h2>\n        <p className=\"text-gray-600 mt-1\">Please provide information about the vehicle</p>\n      </div>\n\n      <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-4\">\n        {/* VIN */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"vin\">VIN (Vehicle Identification Number) *</Label>\n          <Input\n            id=\"vin\"\n            placeholder=\"e.g., 1HGBH41JXMN109186\"\n            maxLength={17}\n            onChange={handleVinChange}\n            className={errors.vin ? 'border-red-500' : ''}\n          />\n          {errors.vin && (\n            <p className=\"text-sm text-red-600\">{errors.vin.message}</p>\n          )}\n          <p className=\"text-xs text-gray-500\">\n            17-character alphanumeric code (excluding I, O, Q)\n          </p>\n        </div>\n\n        {/* Condition */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"condition\">Condition *</Label>\n          <Select\n            value={condition}\n            onValueChange={(value) => setValue('condition', value as any, { shouldValidate: true })}\n          >\n            <SelectTrigger className={errors.condition ? 'border-red-500' : ''}>\n              <SelectValue placeholder=\"Select car condition\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"New\">New</SelectItem>\n              <SelectItem value=\"Preowned\">Preowned</SelectItem>\n            </SelectContent>\n          </Select>\n          {errors.condition && (\n            <p className=\"text-sm text-red-600\">{errors.condition.message}</p>\n          )}\n        </div>\n\n        {/* Year */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"year\">Year *</Label>\n          <Input\n            id=\"year\"\n            type=\"number\"\n            min=\"1990\"\n            max={currentYear + 1}\n            placeholder={currentYear.toString()}\n            {...register('year', {\n              valueAsNumber: true,\n            })}\n            className={errors.year ? 'border-red-500' : ''}\n          />\n          {errors.year && (\n            <p className=\"text-sm text-red-600\">{errors.year.message}</p>\n          )}\n        </div>\n\n        {/* Make */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"make\">Make *</Label>\n          <Select\n            value={make}\n            onValueChange={(value) => setValue('make', value, { shouldValidate: true })}\n          >\n            <SelectTrigger className={errors.make ? 'border-red-500' : ''}>\n              <SelectValue placeholder=\"Select car make\" />\n            </SelectTrigger>\n            <SelectContent>\n              {carMakes.map((carMake) => (\n                <SelectItem key={carMake} value={carMake}>\n                  {carMake}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n          {errors.make && (\n            <p className=\"text-sm text-red-600\">{errors.make.message}</p>\n          )}\n        </div>\n\n        {/* Model */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"model\">Model *</Label>\n          <Input\n            id=\"model\"\n            placeholder=\"e.g., Golf\"\n            {...register('model')}\n            className={errors.model ? 'border-red-500' : ''}\n          />\n          {errors.model && (\n            <p className=\"text-sm text-red-600\">{errors.model.message}</p>\n          )}\n        </div>\n\n        {/* Trim */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"trim\">Trim *</Label>\n          <Input\n            id=\"trim\"\n            placeholder=\"e.g., SE, GTI, Sport\"\n            {...register('trim')}\n            className={errors.trim ? 'border-red-500' : ''}\n          />\n          {errors.trim && (\n            <p className=\"text-sm text-red-600\">{errors.trim.message}</p>\n          )}\n        </div>\n\n        {/* Car Summary */}\n        {make && (\n          <div className=\"p-4 bg-green-50 rounded-md border border-green-200\">\n            <h3 className=\"font-semibold text-green-900 mb-2\">Vehicle Summary</h3>\n            <div className=\"text-sm text-green-800\">\n              <p>\n                {watch('year')} {make} {watch('model')} {watch('trim')}\n              </p>\n              <p>Condition: {condition}</p>\n              {watch('vin') && <p>VIN: {watch('vin')}</p>}\n            </div>\n          </div>\n        )}\n\n        {/* Navigation Buttons */}\n        <div className=\"flex gap-4 pt-4\">\n          {onBack && (\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={onBack}\n              className=\"flex-1\"\n            >\n              Back\n            </Button>\n          )}\n          <Button\n            type=\"submit\"\n            disabled={!isValid || isLoading}\n            className=\"flex-1\"\n          >\n            {isLoading ? 'Saving...' : 'Continue to Documents'}\n          </Button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAmBA,MAAM,cAAc,IAAI,OAAO,WAAW;AAC1C,MAAM,WAAW;IACf;IAAQ;IAAO;IAAW;IAAQ;IAAQ;IAAS;IAAW;IAC9D;IAAiB;IAAU;IAAQ;IAAW;IAAW;IAAQ;IACjE;IAAU;IAAc;IAAS;CAClC;AAEM,SAAS,eAAe,EAC7B,WAAW,EACX,QAAQ,EACR,MAAM,EACN,MAAM,EACN,YAAY,KAAK,EACG;IACpB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC/B,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,yHAAA,CAAA,mBAAgB;QACtC,eAAe,eAAe;YAC5B,KAAK;YACL,WAAW;YACX,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA,MAAM;IACR;IAEA,MAAM,YAAY,MAAM;IACxB,MAAM,OAAO,MAAM;IAEnB,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,IAAI,QAAQ;YACV;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,qBAAqB;QACxE,SAAS,OAAO,OAAO;YAAE,gBAAgB;QAAK;IAChD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAGpC,8OAAC;gBAAK,UAAU,aAAa;gBAAmB,WAAU;;kCAExD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAM;;;;;;0CACrB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,WAAW;gCACX,UAAU;gCACV,WAAW,OAAO,GAAG,GAAG,mBAAmB;;;;;;4BAE5C,OAAO,GAAG,kBACT,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,GAAG,CAAC,OAAO;;;;;;0CAEzD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAMvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,eAAe,CAAC,QAAU,SAAS,aAAa,OAAc;wCAAE,gBAAgB;oCAAK;;kDAErF,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAW,OAAO,SAAS,GAAG,mBAAmB;kDAC9D,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;;;;;;;;;;;;;4BAGhC,OAAO,SAAS,kBACf,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;kCAKjE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAO;;;;;;0CACtB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,KAAI;gCACJ,KAAK,cAAc;gCACnB,aAAa,YAAY,QAAQ;gCAChC,GAAG,SAAS,QAAQ;oCACnB,eAAe;gCACjB,EAAE;gCACF,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;4BAE7C,OAAO,IAAI,kBACV,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;kCAK5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAO;;;;;;0CACtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,eAAe,CAAC,QAAU,SAAS,QAAQ,OAAO;wCAAE,gBAAgB;oCAAK;;kDAEzE,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAW,OAAO,IAAI,GAAG,mBAAmB;kDACzD,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;kDACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,kIAAA,CAAA,aAAU;gDAAe,OAAO;0DAC9B;+CADc;;;;;;;;;;;;;;;;4BAMtB,OAAO,IAAI,kBACV,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;kCAK5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAQ;;;;;;0CACvB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACX,GAAG,SAAS,QAAQ;gCACrB,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;4BAE9C,OAAO,KAAK,kBACX,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAK7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAO;;;;;;0CACtB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACX,GAAG,SAAS,OAAO;gCACpB,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;4BAE7C,OAAO,IAAI,kBACV,8OAAC;gCAAE,WAAU;0CAAwB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;oBAK3D,sBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CACE,MAAM;4CAAQ;4CAAE;4CAAK;4CAAE,MAAM;4CAAS;4CAAE,MAAM;;;;;;;kDAEjD,8OAAC;;4CAAE;4CAAY;;;;;;;oCACd,MAAM,wBAAU,8OAAC;;4CAAE;4CAAM,MAAM;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC;wBAAI,WAAU;;4BACZ,wBACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;0CAIH,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU,CAAC,WAAW;gCACtB,WAAU;0CAET,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 1947, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/ui/file-upload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState } from 'react';\nimport { Upload, X, FileText, Image, AlertCircle } from 'lucide-react';\nimport { cn, formatFileSize, isValidFileType } from '@/lib/utils';\nimport { Button } from './button';\n\ninterface FileUploadProps {\n  onFilesChange: (files: File[]) => void;\n  maxFiles?: number;\n  maxFileSize?: number; // in bytes\n  accept?: string;\n  className?: string;\n}\n\ninterface FileWithPreview extends File {\n  preview?: string;\n  id: string;\n}\n\nexport function FileUpload({\n  onFilesChange,\n  maxFiles = 10,\n  maxFileSize = 10 * 1024 * 1024, // 10MB\n  accept = '.pdf,.jpg,.jpeg,.png,.webp,.doc,.docx',\n  className,\n}: FileUploadProps) {\n  const [files, setFiles] = useState<FileWithPreview[]>([]);\n  const [dragActive, setDragActive] = useState(false);\n  const [errors, setErrors] = useState<string[]>([]);\n\n  const handleFiles = useCallback(\n    (newFiles: FileList | File[]) => {\n      const fileArray = Array.from(newFiles);\n      const validFiles: FileWithPreview[] = [];\n      const newErrors: string[] = [];\n\n      fileArray.forEach((file) => {\n        // Check file type\n        if (!isValidFileType(file)) {\n          newErrors.push(`${file.name}: Invalid file type`);\n          return;\n        }\n\n        // Check file size\n        if (file.size > maxFileSize) {\n          newErrors.push(`${file.name}: File too large (max ${formatFileSize(maxFileSize)})`);\n          return;\n        }\n\n        // Check if we're exceeding max files\n        if (files.length + validFiles.length >= maxFiles) {\n          newErrors.push(`Maximum ${maxFiles} files allowed`);\n          return;\n        }\n\n        // Check for duplicates\n        const isDuplicate = files.some(existingFile => \n          existingFile.name === file.name && existingFile.size === file.size\n        );\n        \n        if (isDuplicate) {\n          newErrors.push(`${file.name}: File already uploaded`);\n          return;\n        }\n\n        const fileWithPreview: FileWithPreview = Object.assign(file, {\n          id: `${file.name}-${Date.now()}-${Math.random()}`,\n          preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,\n        });\n\n        validFiles.push(fileWithPreview);\n      });\n\n      if (validFiles.length > 0) {\n        const updatedFiles = [...files, ...validFiles];\n        setFiles(updatedFiles);\n        onFilesChange(updatedFiles);\n      }\n\n      setErrors(newErrors);\n    },\n    [files, maxFiles, maxFileSize, onFilesChange]\n  );\n\n  const removeFile = useCallback(\n    (fileId: string) => {\n      const updatedFiles = files.filter((file) => file.id !== fileId);\n      setFiles(updatedFiles);\n      onFilesChange(updatedFiles);\n      \n      // Clean up preview URLs\n      const fileToRemove = files.find(file => file.id === fileId);\n      if (fileToRemove?.preview) {\n        URL.revokeObjectURL(fileToRemove.preview);\n      }\n    },\n    [files, onFilesChange]\n  );\n\n  const handleDrag = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  }, []);\n\n  const handleDrop = useCallback(\n    (e: React.DragEvent) => {\n      e.preventDefault();\n      e.stopPropagation();\n      setDragActive(false);\n\n      if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n        handleFiles(e.dataTransfer.files);\n      }\n    },\n    [handleFiles]\n  );\n\n  const handleInputChange = useCallback(\n    (e: React.ChangeEvent<HTMLInputElement>) => {\n      if (e.target.files && e.target.files[0]) {\n        handleFiles(e.target.files);\n      }\n    },\n    [handleFiles]\n  );\n\n  const getFileIcon = (file: File) => {\n    if (file.type.startsWith('image/')) {\n      return <Image className=\"h-8 w-8 text-blue-500\" />;\n    }\n    return <FileText className=\"h-8 w-8 text-gray-500\" />;\n  };\n\n  return (\n    <div className={cn('w-full', className)}>\n      {/* Upload Area */}\n      <div\n        className={cn(\n          'relative border-2 border-dashed rounded-lg p-6 transition-colors',\n          dragActive\n            ? 'border-blue-500 bg-blue-50'\n            : 'border-gray-300 hover:border-gray-400',\n          'focus-within:border-blue-500 focus-within:bg-blue-50'\n        )}\n        onDragEnter={handleDrag}\n        onDragLeave={handleDrag}\n        onDragOver={handleDrag}\n        onDrop={handleDrop}\n      >\n        <input\n          type=\"file\"\n          multiple\n          accept={accept}\n          onChange={handleInputChange}\n          className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n        />\n        \n        <div className=\"text-center\">\n          <Upload className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <div className=\"mt-4\">\n            <p className=\"text-sm text-gray-600\">\n              <span className=\"font-medium text-blue-600 hover:text-blue-500\">\n                Click to upload\n              </span>{' '}\n              or drag and drop\n            </p>\n            <p className=\"text-xs text-gray-500 mt-1\">\n              PDF, JPG, PNG, WEBP, DOC, DOCX up to {formatFileSize(maxFileSize)}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Error Messages */}\n      {errors.length > 0 && (\n        <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-md\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-800\">Upload Errors</h3>\n              <ul className=\"mt-2 text-sm text-red-700 list-disc list-inside\">\n                {errors.map((error, index) => (\n                  <li key={index}>{error}</li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* File List */}\n      {files.length > 0 && (\n        <div className=\"mt-4 space-y-2\">\n          <h4 className=\"text-sm font-medium text-gray-900\">\n            Uploaded Files ({files.length}/{maxFiles})\n          </h4>\n          <div className=\"space-y-2\">\n            {files.map((file) => (\n              <div\n                key={file.id}\n                className=\"flex items-center justify-between p-3 bg-gray-50 rounded-md\"\n              >\n                <div className=\"flex items-center space-x-3\">\n                  {file.preview ? (\n                    <img\n                      src={file.preview}\n                      alt={file.name}\n                      className=\"h-8 w-8 object-cover rounded\"\n                    />\n                  ) : (\n                    getFileIcon(file)\n                  )}\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900 truncate max-w-xs\">\n                      {file.name}\n                    </p>\n                    <p className=\"text-xs text-gray-500\">{formatFileSize(file.size)}</p>\n                  </div>\n                </div>\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => removeFile(file.id)}\n                  className=\"text-red-500 hover:text-red-700\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAoBO,SAAS,WAAW,EACzB,aAAa,EACb,WAAW,EAAE,EACb,cAAc,KAAK,OAAO,IAAI,EAC9B,SAAS,uCAAuC,EAChD,SAAS,EACO;IAChB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEjD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC,MAAM,YAAY,MAAM,IAAI,CAAC;QAC7B,MAAM,aAAgC,EAAE;QACxC,MAAM,YAAsB,EAAE;QAE9B,UAAU,OAAO,CAAC,CAAC;YACjB,kBAAkB;YAClB,IAAI,CAAC,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;gBAC1B,UAAU,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,mBAAmB,CAAC;gBAChD;YACF;YAEA,kBAAkB;YAClB,IAAI,KAAK,IAAI,GAAG,aAAa;gBAC3B,UAAU,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,sBAAsB,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,CAAC,CAAC;gBAClF;YACF;YAEA,qCAAqC;YACrC,IAAI,MAAM,MAAM,GAAG,WAAW,MAAM,IAAI,UAAU;gBAChD,UAAU,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,cAAc,CAAC;gBAClD;YACF;YAEA,uBAAuB;YACvB,MAAM,cAAc,MAAM,IAAI,CAAC,CAAA,eAC7B,aAAa,IAAI,KAAK,KAAK,IAAI,IAAI,aAAa,IAAI,KAAK,KAAK,IAAI;YAGpE,IAAI,aAAa;gBACf,UAAU,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,uBAAuB,CAAC;gBACpD;YACF;YAEA,MAAM,kBAAmC,OAAO,MAAM,CAAC,MAAM;gBAC3D,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI;gBACjD,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI,eAAe,CAAC,QAAQ;YACxE;YAEA,WAAW,IAAI,CAAC;QAClB;QAEA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,MAAM,eAAe;mBAAI;mBAAU;aAAW;YAC9C,SAAS;YACT,cAAc;QAChB;QAEA,UAAU;IACZ,GACA;QAAC;QAAO;QAAU;QAAa;KAAc;IAG/C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC3B,CAAC;QACC,MAAM,eAAe,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;QACxD,SAAS;QACT,cAAc;QAEd,wBAAwB;QACxB,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACpD,IAAI,cAAc,SAAS;YACzB,IAAI,eAAe,CAAC,aAAa,OAAO;QAC1C;IACF,GACA;QAAC;QAAO;KAAc;IAGxB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC3B,CAAC;QACC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,YAAY,EAAE,YAAY,CAAC,KAAK;QAClC;IACF,GACA;QAAC;KAAY;IAGf,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,YAAY,EAAE,MAAM,CAAC,KAAK;QAC5B;IACF,GACA;QAAC;KAAY;IAGf,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAClC,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B;QACA,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;0BAE3B,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA,aACI,+BACA,yCACJ;gBAEF,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,QAAQ;;kCAER,8OAAC;wBACC,MAAK;wBACL,QAAQ;wBACR,QAAQ;wBACR,UAAU;wBACV,WAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAgD;;;;;;4CAExD;4CAAI;;;;;;;kDAGd,8OAAC;wCAAE,WAAU;;4CAA6B;4CACF,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;YAO5D,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;8CACX,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;YASpB,MAAM,MAAM,GAAG,mBACd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAoC;4BAC/B,MAAM,MAAM;4BAAC;4BAAE;4BAAS;;;;;;;kCAE3C,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO,iBACX,8OAAC;gDACC,KAAK,KAAK,OAAO;gDACjB,KAAK,KAAK,IAAI;gDACd,WAAU;;;;;uDAGZ,YAAY;0DAEd,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEACV,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;kEAAyB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;;;;;;;;kDAGlE,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,WAAW,KAAK,EAAE;wCACjC,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BA3BV,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AAoC5B", "debugId": null}}, {"offset": {"line": 2321, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/forms/document-upload-form.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { FileUpload } from '@/components/ui/file-upload';\nimport { Button } from '@/components/ui/button';\nimport { CheckCircle, AlertCircle } from 'lucide-react';\n\ninterface DocumentUploadFormProps {\n  onSubmit: (files: File[]) => void;\n  onBack?: () => void;\n  isLoading?: boolean;\n}\n\nconst requiredDocuments = [\n  'Valid ID (DNI/Passport)',\n  'Proof of Income (Pay stubs, tax returns)',\n  'Bank statements (last 3 months)',\n  'Vehicle registration (if trade-in)',\n  'Insurance documentation',\n];\n\nconst optionalDocuments = [\n  'Employment verification letter',\n  'Additional income documentation',\n  'Co-signer information',\n  'Vehicle inspection report',\n];\n\nexport function DocumentUploadForm({\n  onSubmit,\n  onBack,\n  isLoading = false,\n}: DocumentUploadFormProps) {\n  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleFilesChange = (files: File[]) => {\n    setUploadedFiles(files);\n  };\n\n  const handleSubmit = async () => {\n    if (uploadedFiles.length === 0) {\n      alert('Please upload at least one document before proceeding.');\n      return;\n    }\n\n    setIsSubmitting(true);\n    try {\n      await onSubmit(uploadedFiles);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900\">Document Upload</h2>\n        <p className=\"text-gray-600 mt-1\">\n          Please upload the required documents to complete your loan application\n        </p>\n      </div>\n\n      {/* Document Requirements */}\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        {/* Required Documents */}\n        <div className=\"space-y-3\">\n          <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n            <AlertCircle className=\"h-5 w-5 text-red-500 mr-2\" />\n            Required Documents\n          </h3>\n          <ul className=\"space-y-2\">\n            {requiredDocuments.map((doc, index) => (\n              <li key={index} className=\"flex items-start\">\n                <div className=\"w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0\" />\n                <span className=\"text-sm text-gray-700\">{doc}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        {/* Optional Documents */}\n        <div className=\"space-y-3\">\n          <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n            <CheckCircle className=\"h-5 w-5 text-green-500 mr-2\" />\n            Optional Documents\n          </h3>\n          <ul className=\"space-y-2\">\n            {optionalDocuments.map((doc, index) => (\n              <li key={index} className=\"flex items-start\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0\" />\n                <span className=\"text-sm text-gray-700\">{doc}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n\n      {/* File Upload Component */}\n      <div className=\"space-y-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Upload Documents</h3>\n        <FileUpload\n          onFilesChange={handleFilesChange}\n          maxFiles={15}\n          maxFileSize={10 * 1024 * 1024} // 10MB\n          accept=\".pdf,.jpg,.jpeg,.png,.webp,.doc,.docx\"\n        />\n      </div>\n\n      {/* Upload Guidelines */}\n      <div className=\"p-4 bg-blue-50 rounded-md border border-blue-200\">\n        <h4 className=\"font-semibold text-blue-900 mb-2\">Upload Guidelines</h4>\n        <ul className=\"text-sm text-blue-800 space-y-1\">\n          <li>• Ensure all documents are clear and legible</li>\n          <li>• Maximum file size: 10MB per file</li>\n          <li>• Accepted formats: PDF, JPG, PNG, WEBP, DOC, DOCX</li>\n          <li>• You can upload up to 15 files total</li>\n          <li>• All personal information should be visible and unredacted</li>\n        </ul>\n      </div>\n\n      {/* Privacy Notice */}\n      <div className=\"p-4 bg-gray-50 rounded-md border\">\n        <h4 className=\"font-semibold text-gray-900 mb-2\">Privacy & Security</h4>\n        <p className=\"text-sm text-gray-700\">\n          Your documents are encrypted and stored securely. We comply with GDPR regulations \n          and will only use your information for loan processing purposes. You can request \n          deletion of your data at any time by contacting our support team.\n        </p>\n      </div>\n\n      {/* Upload Status */}\n      {uploadedFiles.length > 0 && (\n        <div className=\"p-4 bg-green-50 rounded-md border border-green-200\">\n          <div className=\"flex items-center\">\n            <CheckCircle className=\"h-5 w-5 text-green-500 mr-2\" />\n            <span className=\"font-semibold text-green-900\">\n              {uploadedFiles.length} document{uploadedFiles.length !== 1 ? 's' : ''} ready for upload\n            </span>\n          </div>\n          <p className=\"text-sm text-green-700 mt-1\">\n            You can proceed with your application or add more documents if needed.\n          </p>\n        </div>\n      )}\n\n      {/* Navigation Buttons */}\n      <div className=\"flex gap-4 pt-4\">\n        {onBack && (\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            onClick={onBack}\n            className=\"flex-1\"\n            disabled={isSubmitting}\n          >\n            Back\n          </Button>\n        )}\n        <Button\n          onClick={handleSubmit}\n          disabled={uploadedFiles.length === 0 || isLoading || isSubmitting}\n          className=\"flex-1\"\n        >\n          {isSubmitting || isLoading ? 'Submitting Application...' : 'Submit Application'}\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAaA,MAAM,oBAAoB;IACxB;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,oBAAoB;IACxB;IACA;IACA;IACA;CACD;AAEM,SAAS,mBAAmB,EACjC,QAAQ,EACR,MAAM,EACN,YAAY,KAAK,EACO;IACxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;IACnB;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,MAAM;YACN;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS;QACjB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAMpC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAA8B;;;;;;;0CAGvD,8OAAC;gCAAG,WAAU;0CACX,kBAAkB,GAAG,CAAC,CAAC,KAAK,sBAC3B,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAyB;;;;;;;uCAFlC;;;;;;;;;;;;;;;;kCASf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAgC;;;;;;;0CAGzD,8OAAC;gCAAG,WAAU;0CACX,kBAAkB,GAAG,CAAC,CAAC,KAAK,sBAC3B,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAyB;;;;;;;uCAFlC;;;;;;;;;;;;;;;;;;;;;;0BAUjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC,0IAAA,CAAA,aAAU;wBACT,eAAe;wBACf,UAAU;wBACV,aAAa,KAAK,OAAO;wBACzB,QAAO;;;;;;;;;;;;0BAKX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;0BAKR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;YAQtC,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAK,WAAU;;oCACb,cAAc,MAAM;oCAAC;oCAAU,cAAc,MAAM,KAAK,IAAI,MAAM;oCAAG;;;;;;;;;;;;;kCAG1E,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;;oBACZ,wBACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;wBACV,UAAU;kCACX;;;;;;kCAIH,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,cAAc,MAAM,KAAK,KAAK,aAAa;wBACrD,WAAU;kCAET,gBAAgB,YAAY,8BAA8B;;;;;;;;;;;;;;;;;;AAKrE", "debugId": null}}, {"offset": {"line": 2768, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/types/loan';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);\n\n// Helper function to create a client with service role key for server-side operations\nexport const createServiceRoleClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;\n  return createClient<Database>(supabaseUrl, serviceRoleKey);\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAGrD,MAAM,0BAA0B;IACrC,MAAM,iBAAiB,QAAQ,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAC7C", "debugId": null}}, {"offset": {"line": 2787, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/current_projects/uniphore/se/kyc-customer/src/components/loan-application.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { PersonalDetailsForm } from './forms/personal-details-form';\nimport { FinancialDetailsForm } from './forms/financial-details-form';\nimport { CarDetailsForm } from './forms/car-details-form';\nimport { DocumentUploadForm } from './forms/document-upload-form';\nimport { PersonalDetailsFormData, FinancialDetailsFormData, CarDetailsFormData } from '@/lib/validations';\nimport { calculateRemainingPrice } from '@/lib/utils';\nimport { supabase } from '@/lib/supabase';\nimport { CheckCircle, Circle, AlertCircle } from 'lucide-react';\n\ntype ApplicationStep = 'personal' | 'financial' | 'car' | 'documents' | 'success';\n\ninterface ApplicationData {\n  personalDetails?: PersonalDetailsFormData;\n  financialDetails?: FinancialDetailsFormData;\n  carDetails?: CarDetailsFormData;\n  documents?: File[];\n}\n\nconst steps = [\n  { id: 'personal', title: 'Personal Details', description: 'Basic information' },\n  { id: 'financial', title: 'Financial Details', description: 'Loan and pricing' },\n  { id: 'car', title: 'Car Details', description: 'Vehicle information' },\n  { id: 'documents', title: 'Documents', description: 'Upload files' },\n];\n\nexport function LoanApplication() {\n  const [currentStep, setCurrentStep] = useState<ApplicationStep>('personal');\n  const [applicationData, setApplicationData] = useState<ApplicationData>({});\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [applicationId, setApplicationId] = useState<string | null>(null);\n\n  const updateApplicationData = useCallback((stepData: Partial<ApplicationData>) => {\n    setApplicationData(prev => ({ ...prev, ...stepData }));\n  }, []);\n\n  const handlePersonalDetailsSubmit = useCallback((data: PersonalDetailsFormData) => {\n    updateApplicationData({ personalDetails: data });\n  }, [updateApplicationData]);\n\n  const handleFinancialDetailsSubmit = useCallback((data: FinancialDetailsFormData) => {\n    updateApplicationData({ financialDetails: data });\n  }, [updateApplicationData]);\n\n  const handleCarDetailsSubmit = useCallback((data: CarDetailsFormData) => {\n    updateApplicationData({ carDetails: data });\n  }, [updateApplicationData]);\n\n  const uploadDocuments = async (files: File[], loanApplicationId: string) => {\n    const uploadPromises = files.map(async (file) => {\n      const fileName = `${loanApplicationId}/${Date.now()}-${file.name}`;\n      \n      // Upload file to Supabase Storage\n      const { data: uploadData, error: uploadError } = await supabase.storage\n        .from('loan-documents')\n        .upload(fileName, file);\n\n      if (uploadError) {\n        throw new Error(`Failed to upload ${file.name}: ${uploadError.message}`);\n      }\n\n      // Save file reference to database\n      const { error: dbError } = await supabase\n        .from('loan_documents')\n        .insert({\n          loan_application_id: loanApplicationId,\n          file_name: file.name,\n          file_size: file.size,\n          file_type: file.type,\n          storage_path: uploadData.path,\n        });\n\n      if (dbError) {\n        throw new Error(`Failed to save document reference: ${dbError.message}`);\n      }\n\n      return uploadData.path;\n    });\n\n    return Promise.all(uploadPromises);\n  };\n\n  const handleDocumentUploadSubmit = async (files: File[]) => {\n    if (!applicationData.personalDetails || !applicationData.financialDetails || !applicationData.carDetails) {\n      setError('Missing application data. Please complete all previous steps.');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Calculate remaining price for financial details\n      const remainingPrice = calculateRemainingPrice(\n        applicationData.financialDetails.totalCarPrice,\n        applicationData.financialDetails.tradeInValue,\n        applicationData.financialDetails.downPayment\n      );\n\n      // Prepare loan application data\n      const loanApplicationData = {\n        personal_details: applicationData.personalDetails,\n        financial_details: {\n          ...applicationData.financialDetails,\n          remainingPrice,\n        },\n        car_details: applicationData.carDetails,\n        status: 'submitted',\n      };\n\n      // Insert loan application\n      const { data: loanApplication, error: loanError } = await supabase\n        .from('loan_applications')\n        .insert(loanApplicationData)\n        .select()\n        .single();\n\n      if (loanError) {\n        throw new Error(`Failed to submit application: ${loanError.message}`);\n      }\n\n      // Upload documents\n      if (files.length > 0) {\n        await uploadDocuments(files, loanApplication.id);\n      }\n\n      setApplicationId(loanApplication.id);\n      setCurrentStep('success');\n    } catch (err) {\n      console.error('Application submission error:', err);\n      setError(err instanceof Error ? err.message : 'Failed to submit application');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getStepStatus = (stepId: string) => {\n    const stepIndex = steps.findIndex(s => s.id === stepId);\n    const currentIndex = steps.findIndex(s => s.id === currentStep);\n    \n    if (stepIndex < currentIndex) return 'completed';\n    if (stepIndex === currentIndex) return 'current';\n    return 'upcoming';\n  };\n\n  const renderStepIndicator = () => (\n    <div className=\"mb-8\">\n      <div className=\"flex items-center justify-between\">\n        {steps.map((step, index) => {\n          const status = getStepStatus(step.id);\n          return (\n            <div key={step.id} className=\"flex items-center\">\n              <div className=\"flex flex-col items-center\">\n                <div className={`\n                  w-10 h-10 rounded-full flex items-center justify-center border-2 transition-colors\n                  ${status === 'completed' ? 'bg-green-500 border-green-500 text-white' : ''}\n                  ${status === 'current' ? 'bg-blue-500 border-blue-500 text-white' : ''}\n                  ${status === 'upcoming' ? 'bg-gray-200 border-gray-300 text-gray-500' : ''}\n                `}>\n                  {status === 'completed' ? (\n                    <CheckCircle className=\"w-6 h-6\" />\n                  ) : (\n                    <span className=\"text-sm font-semibold\">{index + 1}</span>\n                  )}\n                </div>\n                <div className=\"mt-2 text-center\">\n                  <div className={`text-sm font-medium ${\n                    status === 'current' ? 'text-blue-600' : \n                    status === 'completed' ? 'text-green-600' : 'text-gray-500'\n                  }`}>\n                    {step.title}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">{step.description}</div>\n                </div>\n              </div>\n              {index < steps.length - 1 && (\n                <div className={`flex-1 h-0.5 mx-4 ${\n                  status === 'completed' ? 'bg-green-500' : 'bg-gray-300'\n                }`} />\n              )}\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n\n  const renderCurrentStep = () => {\n    switch (currentStep) {\n      case 'personal':\n        return (\n          <PersonalDetailsForm\n            initialData={applicationData.personalDetails}\n            onSubmit={handlePersonalDetailsSubmit}\n            onNext={() => setCurrentStep('financial')}\n            isLoading={isLoading}\n          />\n        );\n      \n      case 'financial':\n        return (\n          <FinancialDetailsForm\n            initialData={applicationData.financialDetails}\n            onSubmit={handleFinancialDetailsSubmit}\n            onNext={() => setCurrentStep('car')}\n            onBack={() => setCurrentStep('personal')}\n            isLoading={isLoading}\n          />\n        );\n      \n      case 'car':\n        return (\n          <CarDetailsForm\n            initialData={applicationData.carDetails}\n            onSubmit={handleCarDetailsSubmit}\n            onNext={() => setCurrentStep('documents')}\n            onBack={() => setCurrentStep('financial')}\n            isLoading={isLoading}\n          />\n        );\n      \n      case 'documents':\n        return (\n          <DocumentUploadForm\n            onSubmit={handleDocumentUploadSubmit}\n            onBack={() => setCurrentStep('car')}\n            isLoading={isLoading}\n          />\n        );\n      \n      case 'success':\n        return (\n          <div className=\"text-center space-y-6\">\n            <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto\">\n              <CheckCircle className=\"w-10 h-10 text-green-500\" />\n            </div>\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900\">Application Submitted Successfully!</h2>\n              <p className=\"text-gray-600 mt-2\">\n                Your loan application has been received and is being processed.\n              </p>\n            </div>\n            {applicationId && (\n              <div className=\"p-4 bg-blue-50 rounded-md border border-blue-200\">\n                <p className=\"text-sm text-blue-800\">\n                  <strong>Application ID:</strong> {applicationId}\n                </p>\n                <p className=\"text-sm text-blue-700 mt-1\">\n                  Please save this ID for your records. You will receive updates via email.\n                </p>\n              </div>\n            )}\n            <div className=\"space-y-2 text-sm text-gray-600\">\n              <p>What happens next:</p>\n              <ul className=\"list-disc list-inside space-y-1\">\n                <li>We'll review your application within 24-48 hours</li>\n                <li>You'll receive an email confirmation shortly</li>\n                <li>Our team may contact you for additional information</li>\n                <li>Final approval decision will be communicated within 5 business days</li>\n              </ul>\n            </div>\n          </div>\n        );\n      \n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6\">\n      {currentStep !== 'success' && renderStepIndicator()}\n      \n      {error && (\n        <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-md\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-800\">Error</h3>\n              <p className=\"text-sm text-red-700 mt-1\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <div className=\"bg-white rounded-lg shadow-lg p-8\">\n        {renderCurrentStep()}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAVA;;;;;;;;;;AAqBA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAY,OAAO;QAAoB,aAAa;IAAoB;IAC9E;QAAE,IAAI;QAAa,OAAO;QAAqB,aAAa;IAAmB;IAC/E;QAAE,IAAI;QAAO,OAAO;QAAe,aAAa;IAAsB;IACtE;QAAE,IAAI;QAAa,OAAO;QAAa,aAAa;IAAe;CACpE;AAEM,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,mBAAmB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,QAAQ;YAAC,CAAC;IACtD,GAAG,EAAE;IAEL,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/C,sBAAsB;YAAE,iBAAiB;QAAK;IAChD,GAAG;QAAC;KAAsB;IAE1B,MAAM,+BAA+B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChD,sBAAsB;YAAE,kBAAkB;QAAK;IACjD,GAAG;QAAC;KAAsB;IAE1B,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,sBAAsB;YAAE,YAAY;QAAK;IAC3C,GAAG;QAAC;KAAsB;IAE1B,MAAM,kBAAkB,OAAO,OAAe;QAC5C,MAAM,iBAAiB,MAAM,GAAG,CAAC,OAAO;YACtC,MAAM,WAAW,GAAG,kBAAkB,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE;YAElE,kCAAkC;YAClC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CACpE,IAAI,CAAC,kBACL,MAAM,CAAC,UAAU;YAEpB,IAAI,aAAa;gBACf,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,YAAY,OAAO,EAAE;YACzE;YAEA,kCAAkC;YAClC,MAAM,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACtC,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,qBAAqB;gBACrB,WAAW,KAAK,IAAI;gBACpB,WAAW,KAAK,IAAI;gBACpB,WAAW,KAAK,IAAI;gBACpB,cAAc,WAAW,IAAI;YAC/B;YAEF,IAAI,SAAS;gBACX,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,QAAQ,OAAO,EAAE;YACzE;YAEA,OAAO,WAAW,IAAI;QACxB;QAEA,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA,MAAM,6BAA6B,OAAO;QACxC,IAAI,CAAC,gBAAgB,eAAe,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,CAAC,gBAAgB,UAAU,EAAE;YACxG,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,kDAAkD;YAClD,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,0BAAuB,AAAD,EAC3C,gBAAgB,gBAAgB,CAAC,aAAa,EAC9C,gBAAgB,gBAAgB,CAAC,YAAY,EAC7C,gBAAgB,gBAAgB,CAAC,WAAW;YAG9C,gCAAgC;YAChC,MAAM,sBAAsB;gBAC1B,kBAAkB,gBAAgB,eAAe;gBACjD,mBAAmB;oBACjB,GAAG,gBAAgB,gBAAgB;oBACnC;gBACF;gBACA,aAAa,gBAAgB,UAAU;gBACvC,QAAQ;YACV;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC/D,IAAI,CAAC,qBACL,MAAM,CAAC,qBACP,MAAM,GACN,MAAM;YAET,IAAI,WAAW;gBACb,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,UAAU,OAAO,EAAE;YACtE;YAEA,mBAAmB;YACnB,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,MAAM,gBAAgB,OAAO,gBAAgB,EAAE;YACjD;YAEA,iBAAiB,gBAAgB,EAAE;YACnC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,MAAM,eAAe,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAEnD,IAAI,YAAY,cAAc,OAAO;QACrC,IAAI,cAAc,cAAc,OAAO;QACvC,OAAO;IACT;IAEA,MAAM,sBAAsB,kBAC1B,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;oBAChB,MAAM,SAAS,cAAc,KAAK,EAAE;oBACpC,qBACE,8OAAC;wBAAkB,WAAU;;0CAC3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC;;kBAEf,EAAE,WAAW,cAAc,6CAA6C,GAAG;kBAC3E,EAAE,WAAW,YAAY,2CAA2C,GAAG;kBACvE,EAAE,WAAW,aAAa,8CAA8C,GAAG;gBAC7E,CAAC;kDACE,WAAW,4BACV,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;iEAEvB,8OAAC;4CAAK,WAAU;sDAAyB,QAAQ;;;;;;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,oBAAoB,EACnC,WAAW,YAAY,kBACvB,WAAW,cAAc,mBAAmB,iBAC5C;0DACC,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;4BAG3D,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;gCAAI,WAAW,CAAC,kBAAkB,EACjC,WAAW,cAAc,iBAAiB,eAC1C;;;;;;;uBA3BI,KAAK,EAAE;;;;;gBA+BrB;;;;;;;;;;;IAKN,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,0JAAA,CAAA,sBAAmB;oBAClB,aAAa,gBAAgB,eAAe;oBAC5C,UAAU;oBACV,QAAQ,IAAM,eAAe;oBAC7B,WAAW;;;;;;YAIjB,KAAK;gBACH,qBACE,8OAAC,2JAAA,CAAA,uBAAoB;oBACnB,aAAa,gBAAgB,gBAAgB;oBAC7C,UAAU;oBACV,QAAQ,IAAM,eAAe;oBAC7B,QAAQ,IAAM,eAAe;oBAC7B,WAAW;;;;;;YAIjB,KAAK;gBACH,qBACE,8OAAC,qJAAA,CAAA,iBAAc;oBACb,aAAa,gBAAgB,UAAU;oBACvC,UAAU;oBACV,QAAQ,IAAM,eAAe;oBAC7B,QAAQ,IAAM,eAAe;oBAC7B,WAAW;;;;;;YAIjB,KAAK;gBACH,qBACE,8OAAC,yJAAA,CAAA,qBAAkB;oBACjB,UAAU;oBACV,QAAQ,IAAM,eAAe;oBAC7B,WAAW;;;;;;YAIjB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;wBAInC,+BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;sDAAO;;;;;;wCAAwB;wCAAE;;;;;;;8CAEpC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;YAMd;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,gBAAgB,aAAa;YAE7B,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;0BAMlD,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT", "debugId": null}}]}