export interface PersonalDetails {
  fullName: string;
  dniNumber: string;
  dateOfBirth: string;
  phoneNumber: string;
  address: string;
  incomeSource: 'Salaried' | 'Self-Employed' | 'Pension';
}

export interface FinancialDetails {
  loanAmount: number;
  totalCarPrice: number;
  tradeInValue: number;
  downPayment: number;
  remainingPrice: number; // calculated field
}

export interface CarDetails {
  vin: string;
  condition: 'New' | 'Preowned';
  year: number;
  make: string;
  model: string;
  trim: string;
}

export interface UploadedDocument {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadedAt: string;
  url?: string;
}

export interface LoanApplication {
  id?: string;
  personalDetails: PersonalDetails;
  financialDetails: FinancialDetails;
  carDetails: CarDetails;
  documents: UploadedDocument[];
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  createdAt?: string;
  updatedAt?: string;
}

export interface LoanApplicationFormData {
  personalDetails: PersonalDetails;
  financialDetails: Omit<FinancialDetails, 'remainingPrice'>;
  carDetails: CarDetails;
}

// Database types for Supabase
export interface Database {
  public: {
    Tables: {
      loan_applications: {
        Row: {
          id: string;
          personal_details: PersonalDetails;
          financial_details: FinancialDetails;
          car_details: CarDetails;
          status: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          personal_details: PersonalDetails;
          financial_details: FinancialDetails;
          car_details: CarDetails;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          personal_details?: PersonalDetails;
          financial_details?: FinancialDetails;
          car_details?: CarDetails;
          status?: string;
          updated_at?: string;
        };
      };
      loan_documents: {
        Row: {
          id: string;
          loan_application_id: string;
          file_name: string;
          file_size: number;
          file_type: string;
          storage_path: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          loan_application_id: string;
          file_name: string;
          file_size: number;
          file_type: string;
          storage_path: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          loan_application_id?: string;
          file_name?: string;
          file_size?: number;
          file_type?: string;
          storage_path?: string;
        };
      };
    };
  };
}
