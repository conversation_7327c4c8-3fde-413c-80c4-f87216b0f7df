import { z } from 'zod';
import { validateDNI, validateVIN } from './utils';

export const personalDetailsSchema = z.object({
  fullName: z
    .string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must be less than 100 characters')
    .regex(/^[a-zA-ZÀ-ÿ\u00f1\u00d1\s]+$/, 'Full name can only contain letters and spaces'),
  
  dniNumber: z
    .string()
    .min(9, 'DNI must be 9 characters')
    .max(9, 'DNI must be 9 characters')
    .refine(validateDNI, 'Invalid DNI format'),
  
  dateOfBirth: z
    .string()
    .min(1, 'Date of birth is required')
    .refine((date) => {
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      return age >= 18 && age <= 100;
    }, 'You must be between 18 and 100 years old'),
  
  phoneNumber: z
    .string()
    .min(9, 'Phone number must be at least 9 digits')
    .regex(/^[0-9\s]+$/, 'Phone number can only contain digits and spaces'),
  
  address: z
    .string()
    .min(10, 'Address must be at least 10 characters')
    .max(200, 'Address must be less than 200 characters'),
  
  incomeSource: z.enum(['Salaried', 'Self-Employed', 'Pension'], {
    required_error: 'Please select an income source',
  }),
});

export const financialDetailsSchema = z.object({
  loanAmount: z
    .number()
    .min(1000, 'Loan amount must be at least €1,000')
    .max(100000, 'Loan amount cannot exceed €100,000'),
  
  totalCarPrice: z
    .number()
    .min(1000, 'Car price must be at least €1,000')
    .max(200000, 'Car price cannot exceed €200,000'),
  
  tradeInValue: z
    .number()
    .min(0, 'Trade-in value cannot be negative')
    .max(150000, 'Trade-in value cannot exceed €150,000'),
  
  downPayment: z
    .number()
    .min(0, 'Down payment cannot be negative')
    .max(100000, 'Down payment cannot exceed €100,000'),
}).refine((data) => {
  const remainingPrice = data.totalCarPrice - data.tradeInValue - data.downPayment;
  return data.loanAmount <= remainingPrice;
}, {
  message: 'Loan amount cannot exceed the remaining price after trade-in and down payment',
  path: ['loanAmount'],
});

export const carDetailsSchema = z.object({
  vin: z
    .string()
    .min(17, 'VIN must be 17 characters')
    .max(17, 'VIN must be 17 characters')
    .refine(validateVIN, 'Invalid VIN format'),
  
  condition: z.enum(['New', 'Preowned'], {
    required_error: 'Please select car condition',
  }),
  
  year: z
    .number()
    .min(1990, 'Car year must be 1990 or later')
    .max(new Date().getFullYear() + 1, 'Car year cannot be in the future'),
  
  make: z
    .string()
    .min(1, 'Car make is required')
    .max(50, 'Car make must be less than 50 characters'),
  
  model: z
    .string()
    .min(1, 'Car model is required')
    .max(50, 'Car model must be less than 50 characters'),
  
  trim: z
    .string()
    .min(1, 'Car trim is required')
    .max(50, 'Car trim must be less than 50 characters'),
});

export const loanApplicationSchema = z.object({
  personalDetails: personalDetailsSchema,
  financialDetails: financialDetailsSchema,
  carDetails: carDetailsSchema,
});

export type PersonalDetailsFormData = z.infer<typeof personalDetailsSchema>;
export type FinancialDetailsFormData = z.infer<typeof financialDetailsSchema>;
export type CarDetailsFormData = z.infer<typeof carDetailsSchema>;
export type LoanApplicationFormData = z.infer<typeof loanApplicationSchema>;
