import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { loanApplicationSchema } from '@/lib/validations';
import { calculateRemainingPrice } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = loanApplicationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid application data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { personalDetails, financialDetails, carDetails } = validationResult.data;

    // Calculate remaining price
    const remainingPrice = calculateRemainingPrice(
      financialDetails.totalCarPrice,
      financialDetails.tradeInValue,
      financialDetails.downPayment
    );

    // Prepare data for database
    const applicationData = {
      personal_details: personalDetails,
      financial_details: {
        ...financialDetails,
        remainingPrice,
      },
      car_details: carDetails,
      status: 'submitted',
    };

    // Insert into database
    const { data, error } = await supabase
      .from('loan_applications')
      .insert(applicationData)
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to submit application' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      applicationId: data.id,
      message: 'Application submitted successfully',
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const applicationId = searchParams.get('id');

    if (applicationId) {
      // Get specific application
      const { data, error } = await supabase
        .from('loan_applications')
        .select(`
          *,
          loan_documents (*)
        `)
        .eq('id', applicationId)
        .single();

      if (error) {
        return NextResponse.json(
          { error: 'Application not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(data);
    } else {
      // Get all applications (for admin use)
      const { data, error } = await supabase
        .from('loan_applications')
        .select(`
          *,
          loan_documents (*)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        return NextResponse.json(
          { error: 'Failed to fetch applications' },
          { status: 500 }
        );
      }

      return NextResponse.json(data);
    }
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
