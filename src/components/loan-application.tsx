'use client';

import React, { useState, useCallback } from 'react';
import { PersonalDetailsForm } from './forms/personal-details-form';
import { FinancialDetailsForm } from './forms/financial-details-form';
import { CarDetailsForm } from './forms/car-details-form';
import { DocumentUploadForm } from './forms/document-upload-form';
import { PersonalDetailsFormData, FinancialDetailsFormData, CarDetailsFormData } from '@/lib/validations';
import { calculateRemainingPrice } from '@/lib/utils';
import { supabase } from '@/lib/supabase';
import { CheckCircle, Circle, AlertCircle } from 'lucide-react';

type ApplicationStep = 'personal' | 'financial' | 'car' | 'documents' | 'success';

interface ApplicationData {
  personalDetails?: PersonalDetailsFormData;
  financialDetails?: FinancialDetailsFormData;
  carDetails?: CarDetailsFormData;
  documents?: File[];
}

const steps = [
  { id: 'personal', title: 'Personal Details', description: 'Basic information' },
  { id: 'financial', title: 'Financial Details', description: 'Loan and pricing' },
  { id: 'car', title: 'Car Details', description: 'Vehicle information' },
  { id: 'documents', title: 'Documents', description: 'Upload files' },
];

export function LoanApplication() {
  const [currentStep, setCurrentStep] = useState<ApplicationStep>('personal');
  const [applicationData, setApplicationData] = useState<ApplicationData>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [applicationId, setApplicationId] = useState<string | null>(null);

  const updateApplicationData = useCallback((stepData: Partial<ApplicationData>) => {
    setApplicationData(prev => ({ ...prev, ...stepData }));
  }, []);

  const handlePersonalDetailsSubmit = useCallback((data: PersonalDetailsFormData) => {
    updateApplicationData({ personalDetails: data });
  }, [updateApplicationData]);

  const handleFinancialDetailsSubmit = useCallback((data: FinancialDetailsFormData) => {
    updateApplicationData({ financialDetails: data });
  }, [updateApplicationData]);

  const handleCarDetailsSubmit = useCallback((data: CarDetailsFormData) => {
    updateApplicationData({ carDetails: data });
  }, [updateApplicationData]);

  const uploadDocuments = async (files: File[], loanApplicationId: string) => {
    const uploadPromises = files.map(async (file) => {
      const fileName = `${loanApplicationId}/${Date.now()}-${file.name}`;
      
      // Upload file to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('loan-documents')
        .upload(fileName, file);

      if (uploadError) {
        throw new Error(`Failed to upload ${file.name}: ${uploadError.message}`);
      }

      // Save file reference to database
      const { error: dbError } = await supabase
        .from('loan_documents')
        .insert({
          loan_application_id: loanApplicationId,
          file_name: file.name,
          file_size: file.size,
          file_type: file.type,
          storage_path: uploadData.path,
        });

      if (dbError) {
        throw new Error(`Failed to save document reference: ${dbError.message}`);
      }

      return uploadData.path;
    });

    return Promise.all(uploadPromises);
  };

  const handleDocumentUploadSubmit = async (files: File[]) => {
    if (!applicationData.personalDetails || !applicationData.financialDetails || !applicationData.carDetails) {
      setError('Missing application data. Please complete all previous steps.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Calculate remaining price for financial details
      const remainingPrice = calculateRemainingPrice(
        applicationData.financialDetails.totalCarPrice,
        applicationData.financialDetails.tradeInValue,
        applicationData.financialDetails.downPayment
      );

      // Prepare loan application data
      const loanApplicationData = {
        personal_details: applicationData.personalDetails,
        financial_details: {
          ...applicationData.financialDetails,
          remainingPrice,
        },
        car_details: applicationData.carDetails,
        status: 'submitted',
      };

      // Insert loan application
      const { data: loanApplication, error: loanError } = await supabase
        .from('loan_applications')
        .insert(loanApplicationData)
        .select()
        .single();

      if (loanError) {
        throw new Error(`Failed to submit application: ${loanError.message}`);
      }

      // Upload documents
      if (files.length > 0) {
        await uploadDocuments(files, loanApplication.id);
      }

      setApplicationId(loanApplication.id);
      setCurrentStep('success');
    } catch (err) {
      console.error('Application submission error:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit application');
    } finally {
      setIsLoading(false);
    }
  };

  const getStepStatus = (stepId: string) => {
    const stepIndex = steps.findIndex(s => s.id === stepId);
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'current';
    return 'upcoming';
  };

  const renderStepIndicator = () => (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const status = getStepStatus(step.id);
          return (
            <div key={step.id} className="flex items-center">
              <div className="flex flex-col items-center">
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center border-2 transition-colors
                  ${status === 'completed' ? 'bg-green-500 border-green-500 text-white' : ''}
                  ${status === 'current' ? 'bg-blue-500 border-blue-500 text-white' : ''}
                  ${status === 'upcoming' ? 'bg-gray-200 border-gray-300 text-gray-500' : ''}
                `}>
                  {status === 'completed' ? (
                    <CheckCircle className="w-6 h-6" />
                  ) : (
                    <span className="text-sm font-semibold">{index + 1}</span>
                  )}
                </div>
                <div className="mt-2 text-center">
                  <div className={`text-sm font-medium ${
                    status === 'current' ? 'text-blue-600' : 
                    status === 'completed' ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </div>
                  <div className="text-xs text-gray-500">{step.description}</div>
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className={`flex-1 h-0.5 mx-4 ${
                  status === 'completed' ? 'bg-green-500' : 'bg-gray-300'
                }`} />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'personal':
        return (
          <PersonalDetailsForm
            initialData={applicationData.personalDetails}
            onSubmit={handlePersonalDetailsSubmit}
            onNext={() => setCurrentStep('financial')}
            isLoading={isLoading}
          />
        );
      
      case 'financial':
        return (
          <FinancialDetailsForm
            initialData={applicationData.financialDetails}
            onSubmit={handleFinancialDetailsSubmit}
            onNext={() => setCurrentStep('car')}
            onBack={() => setCurrentStep('personal')}
            isLoading={isLoading}
          />
        );
      
      case 'car':
        return (
          <CarDetailsForm
            initialData={applicationData.carDetails}
            onSubmit={handleCarDetailsSubmit}
            onNext={() => setCurrentStep('documents')}
            onBack={() => setCurrentStep('financial')}
            isLoading={isLoading}
          />
        );
      
      case 'documents':
        return (
          <DocumentUploadForm
            onSubmit={handleDocumentUploadSubmit}
            onBack={() => setCurrentStep('car')}
            isLoading={isLoading}
          />
        );
      
      case 'success':
        return (
          <div className="text-center space-y-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="w-10 h-10 text-green-500" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Application Submitted Successfully!</h2>
              <p className="text-gray-600 mt-2">
                Your loan application has been received and is being processed.
              </p>
            </div>
            {applicationId && (
              <div className="p-4 bg-blue-50 rounded-md border border-blue-200">
                <p className="text-sm text-blue-800">
                  <strong>Application ID:</strong> {applicationId}
                </p>
                <p className="text-sm text-blue-700 mt-1">
                  Please save this ID for your records. You will receive updates via email.
                </p>
              </div>
            )}
            <div className="space-y-2 text-sm text-gray-600">
              <p>What happens next:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>We'll review your application within 24-48 hours</li>
                <li>You'll receive an email confirmation shortly</li>
                <li>Our team may contact you for additional information</li>
                <li>Final approval decision will be communicated within 5 business days</li>
              </ul>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {currentStep !== 'success' && renderStepIndicator()}
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-white rounded-lg shadow-lg p-8">
        {renderCurrentStep()}
      </div>
    </div>
  );
}
