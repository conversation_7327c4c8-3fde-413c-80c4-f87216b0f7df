'use client';

import React, { useState } from 'react';
import { FileUpload } from '@/components/ui/file-upload';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle } from 'lucide-react';

interface DocumentUploadFormProps {
  onSubmit: (files: File[]) => void;
  onBack?: () => void;
  isLoading?: boolean;
}

const requiredDocuments = [
  'Valid ID (DNI/Passport)',
  'Proof of Income (Pay stubs, tax returns)',
  'Bank statements (last 3 months)',
  'Vehicle registration (if trade-in)',
  'Insurance documentation',
];

const optionalDocuments = [
  'Employment verification letter',
  'Additional income documentation',
  'Co-signer information',
  'Vehicle inspection report',
];

export function DocumentUploadForm({
  onSubmit,
  onBack,
  isLoading = false,
}: DocumentUploadFormProps) {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFilesChange = (files: File[]) => {
    setUploadedFiles(files);
  };

  const handleSubmit = async () => {
    if (uploadedFiles.length === 0) {
      alert('Please upload at least one document before proceeding.');
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(uploadedFiles);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Document Upload</h2>
        <p className="text-gray-600 mt-1">
          Please upload the required documents to complete your loan application
        </p>
      </div>

      {/* Document Requirements */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Required Documents */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            Required Documents
          </h3>
          <ul className="space-y-2">
            {requiredDocuments.map((doc, index) => (
              <li key={index} className="flex items-start">
                <div className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                <span className="text-sm text-gray-700">{doc}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Optional Documents */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            Optional Documents
          </h3>
          <ul className="space-y-2">
            {optionalDocuments.map((doc, index) => (
              <li key={index} className="flex items-start">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                <span className="text-sm text-gray-700">{doc}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* File Upload Component */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Upload Documents</h3>
        <FileUpload
          onFilesChange={handleFilesChange}
          maxFiles={15}
          maxFileSize={10 * 1024 * 1024} // 10MB
          accept=".pdf,.jpg,.jpeg,.png,.webp,.doc,.docx"
        />
      </div>

      {/* Upload Guidelines */}
      <div className="p-4 bg-blue-50 rounded-md border border-blue-200">
        <h4 className="font-semibold text-blue-900 mb-2">Upload Guidelines</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Ensure all documents are clear and legible</li>
          <li>• Maximum file size: 10MB per file</li>
          <li>• Accepted formats: PDF, JPG, PNG, WEBP, DOC, DOCX</li>
          <li>• You can upload up to 15 files total</li>
          <li>• All personal information should be visible and unredacted</li>
        </ul>
      </div>

      {/* Privacy Notice */}
      <div className="p-4 bg-gray-50 rounded-md border">
        <h4 className="font-semibold text-gray-900 mb-2">Privacy & Security</h4>
        <p className="text-sm text-gray-700">
          Your documents are encrypted and stored securely. We comply with GDPR regulations 
          and will only use your information for loan processing purposes. You can request 
          deletion of your data at any time by contacting our support team.
        </p>
      </div>

      {/* Upload Status */}
      {uploadedFiles.length > 0 && (
        <div className="p-4 bg-green-50 rounded-md border border-green-200">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            <span className="font-semibold text-green-900">
              {uploadedFiles.length} document{uploadedFiles.length !== 1 ? 's' : ''} ready for upload
            </span>
          </div>
          <p className="text-sm text-green-700 mt-1">
            You can proceed with your application or add more documents if needed.
          </p>
        </div>
      )}

      {/* Navigation Buttons */}
      <div className="flex gap-4 pt-4">
        {onBack && (
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            className="flex-1"
            disabled={isSubmitting}
          >
            Back
          </Button>
        )}
        <Button
          onClick={handleSubmit}
          disabled={uploadedFiles.length === 0 || isLoading || isSubmitting}
          className="flex-1"
        >
          {isSubmitting || isLoading ? 'Submitting Application...' : 'Submit Application'}
        </Button>
      </div>
    </div>
  );
}
