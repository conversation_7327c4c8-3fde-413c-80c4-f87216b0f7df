'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { carDetailsSchema, CarDetailsFormData } from '@/lib/validations';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

interface CarDetailsFormProps {
  initialData?: Partial<CarDetailsFormData>;
  onSubmit: (data: CarDetailsFormData) => void;
  onNext?: () => void;
  onBack?: () => void;
  isLoading?: boolean;
}

const currentYear = new Date().getFullYear();
const carMakes = [
  'Audi', 'BMW', 'Citroën', 'Fiat', 'Ford', 'Honda', 'Hyundai', 'Kia',
  'Mercedes-Benz', 'Nissan', 'Opel', 'Peugeot', 'Renault', 'SEAT', 'Škoda',
  'Toyota', 'Volkswagen', 'Volvo', 'Other'
];

export function CarDetailsForm({
  initialData,
  onSubmit,
  onNext,
  onBack,
  isLoading = false,
}: CarDetailsFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<CarDetailsFormData>({
    resolver: zodResolver(carDetailsSchema),
    defaultValues: initialData || {
      vin: '',
      condition: undefined,
      year: currentYear,
      make: '',
      model: '',
      trim: '',
    },
    mode: 'onChange',
  });

  const condition = watch('condition');
  const make = watch('make');

  const handleFormSubmit = (data: CarDetailsFormData) => {
    onSubmit(data);
    if (onNext) {
      onNext();
    }
  };

  const handleVinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase().replace(/[^A-HJ-NPR-Z0-9]/g, '');
    setValue('vin', value, { shouldValidate: true });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Car Details</h2>
        <p className="text-gray-600 mt-1">Please provide information about the vehicle</p>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        {/* VIN */}
        <div className="space-y-2">
          <Label htmlFor="vin">VIN (Vehicle Identification Number) *</Label>
          <Input
            id="vin"
            placeholder="e.g., 1HGBH41JXMN109186"
            maxLength={17}
            onChange={handleVinChange}
            className={errors.vin ? 'border-red-500' : ''}
          />
          {errors.vin && (
            <p className="text-sm text-red-600">{errors.vin.message}</p>
          )}
          <p className="text-xs text-gray-500">
            17-character alphanumeric code (excluding I, O, Q)
          </p>
        </div>

        {/* Condition */}
        <div className="space-y-2">
          <Label htmlFor="condition">Condition *</Label>
          <Select
            value={condition}
            onValueChange={(value) => setValue('condition', value as any, { shouldValidate: true })}
          >
            <SelectTrigger className={errors.condition ? 'border-red-500' : ''}>
              <SelectValue placeholder="Select car condition" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="New">New</SelectItem>
              <SelectItem value="Preowned">Preowned</SelectItem>
            </SelectContent>
          </Select>
          {errors.condition && (
            <p className="text-sm text-red-600">{errors.condition.message}</p>
          )}
        </div>

        {/* Year */}
        <div className="space-y-2">
          <Label htmlFor="year">Year *</Label>
          <Input
            id="year"
            type="number"
            min="1990"
            max={currentYear + 1}
            placeholder={currentYear.toString()}
            {...register('year', {
              valueAsNumber: true,
            })}
            className={errors.year ? 'border-red-500' : ''}
          />
          {errors.year && (
            <p className="text-sm text-red-600">{errors.year.message}</p>
          )}
        </div>

        {/* Make */}
        <div className="space-y-2">
          <Label htmlFor="make">Make *</Label>
          <Select
            value={make}
            onValueChange={(value) => setValue('make', value, { shouldValidate: true })}
          >
            <SelectTrigger className={errors.make ? 'border-red-500' : ''}>
              <SelectValue placeholder="Select car make" />
            </SelectTrigger>
            <SelectContent>
              {carMakes.map((carMake) => (
                <SelectItem key={carMake} value={carMake}>
                  {carMake}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.make && (
            <p className="text-sm text-red-600">{errors.make.message}</p>
          )}
        </div>

        {/* Model */}
        <div className="space-y-2">
          <Label htmlFor="model">Model *</Label>
          <Input
            id="model"
            placeholder="e.g., Golf"
            {...register('model')}
            className={errors.model ? 'border-red-500' : ''}
          />
          {errors.model && (
            <p className="text-sm text-red-600">{errors.model.message}</p>
          )}
        </div>

        {/* Trim */}
        <div className="space-y-2">
          <Label htmlFor="trim">Trim *</Label>
          <Input
            id="trim"
            placeholder="e.g., SE, GTI, Sport"
            {...register('trim')}
            className={errors.trim ? 'border-red-500' : ''}
          />
          {errors.trim && (
            <p className="text-sm text-red-600">{errors.trim.message}</p>
          )}
        </div>

        {/* Car Summary */}
        {make && (
          <div className="p-4 bg-green-50 rounded-md border border-green-200">
            <h3 className="font-semibold text-green-900 mb-2">Vehicle Summary</h3>
            <div className="text-sm text-green-800">
              <p>
                {watch('year')} {make} {watch('model')} {watch('trim')}
              </p>
              <p>Condition: {condition}</p>
              {watch('vin') && <p>VIN: {watch('vin')}</p>}
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex gap-4 pt-4">
          {onBack && (
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              className="flex-1"
            >
              Back
            </Button>
          )}
          <Button
            type="submit"
            disabled={!isValid || isLoading}
            className="flex-1"
          >
            {isLoading ? 'Saving...' : 'Continue to Documents'}
          </Button>
        </div>
      </form>
    </div>
  );
}
