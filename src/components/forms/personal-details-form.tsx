'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { personalDetailsSchema, PersonalDetailsFormData } from '@/lib/validations';
import { formatPhoneNumber } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

interface PersonalDetailsFormProps {
  initialData?: Partial<PersonalDetailsFormData>;
  onSubmit: (data: PersonalDetailsFormData) => void;
  onNext?: () => void;
  isLoading?: boolean;
}

export function PersonalDetailsForm({
  initialData,
  onSubmit,
  onNext,
  isLoading = false,
}: PersonalDetailsFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<PersonalDetailsFormData>({
    resolver: zodResolver(personalDetailsSchema),
    defaultValues: initialData || {
      fullName: '',
      dniNumber: '',
      dateOfBirth: '',
      phoneNumber: '',
      address: '',
      incomeSource: undefined,
    },
    mode: 'onChange',
  });

  const phoneNumber = watch('phoneNumber');
  const incomeSource = watch('incomeSource');

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setValue('phoneNumber', formatted, { shouldValidate: true });
  };

  const handleFormSubmit = (data: PersonalDetailsFormData) => {
    onSubmit(data);
    if (onNext) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Personal Details</h2>
        <p className="text-gray-600 mt-1">Please provide your personal information</p>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        {/* Full Name */}
        <div className="space-y-2">
          <Label htmlFor="fullName">Full Name *</Label>
          <Input
            id="fullName"
            placeholder="e.g., RAMÓN GOMEZ DE LA CRUZ"
            {...register('fullName')}
            className={errors.fullName ? 'border-red-500' : ''}
          />
          {errors.fullName && (
            <p className="text-sm text-red-600">{errors.fullName.message}</p>
          )}
        </div>

        {/* DNI Number */}
        <div className="space-y-2">
          <Label htmlFor="dniNumber">DNI Number *</Label>
          <Input
            id="dniNumber"
            placeholder="e.g., 01234567A"
            maxLength={9}
            {...register('dniNumber')}
            className={errors.dniNumber ? 'border-red-500' : ''}
          />
          {errors.dniNumber && (
            <p className="text-sm text-red-600">{errors.dniNumber.message}</p>
          )}
        </div>

        {/* Date of Birth */}
        <div className="space-y-2">
          <Label htmlFor="dateOfBirth">Date of Birth *</Label>
          <Input
            id="dateOfBirth"
            type="date"
            {...register('dateOfBirth')}
            className={errors.dateOfBirth ? 'border-red-500' : ''}
          />
          {errors.dateOfBirth && (
            <p className="text-sm text-red-600">{errors.dateOfBirth.message}</p>
          )}
        </div>

        {/* Phone Number */}
        <div className="space-y-2">
          <Label htmlFor="phoneNumber">Phone Number *</Label>
          <Input
            id="phoneNumber"
            placeholder="e.g., 678 159 753"
            value={phoneNumber}
            onChange={handlePhoneChange}
            className={errors.phoneNumber ? 'border-red-500' : ''}
          />
          {errors.phoneNumber && (
            <p className="text-sm text-red-600">{errors.phoneNumber.message}</p>
          )}
        </div>

        {/* Address */}
        <div className="space-y-2">
          <Label htmlFor="address">Address *</Label>
          <Textarea
            id="address"
            placeholder="e.g., Avenida de Concha Espina, 1, 28036 Madrid, Spain"
            rows={3}
            {...register('address')}
            className={errors.address ? 'border-red-500' : ''}
          />
          {errors.address && (
            <p className="text-sm text-red-600">{errors.address.message}</p>
          )}
        </div>

        {/* Income Source */}
        <div className="space-y-2">
          <Label htmlFor="incomeSource">Income Source *</Label>
          <Select
            value={incomeSource}
            onValueChange={(value) => setValue('incomeSource', value as any, { shouldValidate: true })}
          >
            <SelectTrigger className={errors.incomeSource ? 'border-red-500' : ''}>
              <SelectValue placeholder="Select your income source" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Salaried">Salaried</SelectItem>
              <SelectItem value="Self-Employed">Self-Employed</SelectItem>
              <SelectItem value="Pension">Pension</SelectItem>
            </SelectContent>
          </Select>
          {errors.incomeSource && (
            <p className="text-sm text-red-600">{errors.incomeSource.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <Button
            type="submit"
            disabled={!isValid || isLoading}
            className="w-full"
          >
            {isLoading ? 'Saving...' : 'Continue to Financial Details'}
          </Button>
        </div>
      </form>
    </div>
  );
}
