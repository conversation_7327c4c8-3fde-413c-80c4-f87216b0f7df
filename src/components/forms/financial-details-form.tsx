'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { financialDetailsSchema, FinancialDetailsFormData } from '@/lib/validations';
import { formatCurrency, calculateRemainingPrice } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

interface FinancialDetailsFormProps {
  initialData?: Partial<FinancialDetailsFormData>;
  onSubmit: (data: FinancialDetailsFormData) => void;
  onNext?: () => void;
  onBack?: () => void;
  isLoading?: boolean;
}

export function FinancialDetailsForm({
  initialData,
  onSubmit,
  onNext,
  onBack,
  isLoading = false,
}: FinancialDetailsFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<FinancialDetailsFormData>({
    resolver: zodResolver(financialDetailsSchema),
    defaultValues: initialData || {
      loanAmount: 0,
      totalCarPrice: 0,
      tradeInValue: 0,
      downPayment: 0,
    },
    mode: 'onChange',
  });

  const totalCarPrice = watch('totalCarPrice');
  const tradeInValue = watch('tradeInValue');
  const downPayment = watch('downPayment');
  const loanAmount = watch('loanAmount');

  const remainingPrice = calculateRemainingPrice(totalCarPrice, tradeInValue, downPayment);

  // Auto-calculate loan amount when other values change
  useEffect(() => {
    if (totalCarPrice > 0) {
      const maxLoanAmount = Math.max(0, remainingPrice);
      if (loanAmount === 0 || loanAmount > maxLoanAmount) {
        setValue('loanAmount', maxLoanAmount, { shouldValidate: true });
      }
    }
  }, [totalCarPrice, tradeInValue, downPayment, remainingPrice, loanAmount, setValue]);

  const handleFormSubmit = (data: FinancialDetailsFormData) => {
    onSubmit(data);
    if (onNext) {
      onNext();
    }
  };

  const formatNumberInput = (value: string): number => {
    const numericValue = value.replace(/[^0-9.]/g, '');
    return parseFloat(numericValue) || 0;
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Financial Details</h2>
        <p className="text-gray-600 mt-1">Please provide your loan and car pricing information</p>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        {/* Total Car Price */}
        <div className="space-y-2">
          <Label htmlFor="totalCarPrice">Total Car Price *</Label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">€</span>
            <Input
              id="totalCarPrice"
              type="number"
              step="0.01"
              min="0"
              placeholder="20,000"
              className={`pl-8 ${errors.totalCarPrice ? 'border-red-500' : ''}`}
              {...register('totalCarPrice', {
                valueAsNumber: true,
              })}
            />
          </div>
          {errors.totalCarPrice && (
            <p className="text-sm text-red-600">{errors.totalCarPrice.message}</p>
          )}
        </div>

        {/* Trade-In Value */}
        <div className="space-y-2">
          <Label htmlFor="tradeInValue">Trade-In Value</Label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">€</span>
            <Input
              id="tradeInValue"
              type="number"
              step="0.01"
              min="0"
              placeholder="10,000"
              className={`pl-8 ${errors.tradeInValue ? 'border-red-500' : ''}`}
              {...register('tradeInValue', {
                valueAsNumber: true,
              })}
            />
          </div>
          {errors.tradeInValue && (
            <p className="text-sm text-red-600">{errors.tradeInValue.message}</p>
          )}
        </div>

        {/* Down Payment */}
        <div className="space-y-2">
          <Label htmlFor="downPayment">Down Payment</Label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">€</span>
            <Input
              id="downPayment"
              type="number"
              step="0.01"
              min="0"
              placeholder="0"
              className={`pl-8 ${errors.downPayment ? 'border-red-500' : ''}`}
              {...register('downPayment', {
                valueAsNumber: true,
              })}
            />
          </div>
          {errors.downPayment && (
            <p className="text-sm text-red-600">{errors.downPayment.message}</p>
          )}
        </div>

        {/* Remaining Price (Calculated) */}
        <div className="space-y-2">
          <Label>Remaining Price (Calculated)</Label>
          <div className="p-3 bg-gray-50 rounded-md border">
            <span className="text-lg font-semibold text-gray-900">
              {formatCurrency(remainingPrice)}
            </span>
            <p className="text-sm text-gray-600 mt-1">
              Total Price - Trade-In Value - Down Payment
            </p>
          </div>
        </div>

        {/* Loan Amount */}
        <div className="space-y-2">
          <Label htmlFor="loanAmount">Loan Amount *</Label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">€</span>
            <Input
              id="loanAmount"
              type="number"
              step="0.01"
              min="0"
              max={remainingPrice}
              placeholder="10,000"
              className={`pl-8 ${errors.loanAmount ? 'border-red-500' : ''}`}
              {...register('loanAmount', {
                valueAsNumber: true,
              })}
            />
          </div>
          {errors.loanAmount && (
            <p className="text-sm text-red-600">{errors.loanAmount.message}</p>
          )}
          <p className="text-sm text-gray-600">
            Maximum loan amount: {formatCurrency(remainingPrice)}
          </p>
        </div>

        {/* Summary */}
        {totalCarPrice > 0 && (
          <div className="p-4 bg-blue-50 rounded-md border border-blue-200">
            <h3 className="font-semibold text-blue-900 mb-2">Loan Summary</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Total Car Price:</span>
                <span>{formatCurrency(totalCarPrice)}</span>
              </div>
              <div className="flex justify-between">
                <span>Trade-In Value:</span>
                <span>-{formatCurrency(tradeInValue)}</span>
              </div>
              <div className="flex justify-between">
                <span>Down Payment:</span>
                <span>-{formatCurrency(downPayment)}</span>
              </div>
              <hr className="my-2 border-blue-300" />
              <div className="flex justify-between font-semibold">
                <span>Loan Amount:</span>
                <span>{formatCurrency(loanAmount)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex gap-4 pt-4">
          {onBack && (
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              className="flex-1"
            >
              Back
            </Button>
          )}
          <Button
            type="submit"
            disabled={!isValid || isLoading}
            className="flex-1"
          >
            {isLoading ? 'Saving...' : 'Continue to Car Details'}
          </Button>
        </div>
      </form>
    </div>
  );
}
